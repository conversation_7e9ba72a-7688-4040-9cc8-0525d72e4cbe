name: Pre-Merge Validation

on:
  pull_request:
    branches:
      - main
      - master
      - production
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'

jobs:
  # 1. CODE QUALITY CHECKS
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci --ignore-scripts
      
      - name: Run Prettier
        run: npm run format:check
      
      - name: Run ESLint
        run: npm run lint
        continue-on-error: false
      
      - name: TypeScript Check - Frontend
        run: npm run type-check
      
      - name: TypeScript Check - Backend
        run: cd backend && npm run type-check
  
  # 2. UNIT TESTS
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    strategy:
      matrix:
        project: [frontend, backend]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies - Frontend
        if: matrix.project == 'frontend'
        run: npm ci --ignore-scripts
      
      - name: Install dependencies - Backend
        if: matrix.project == 'backend'
        run: cd backend && npm ci
      
      - name: Run Frontend Tests
        if: matrix.project == 'frontend'
        run: npm run test:coverage
      
      - name: Run Backend Tests
        if: matrix.project == 'backend'
        run: cd backend && npm test -- --coverage
      
      - name: Upload Coverage
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: ${{ matrix.project }}
          fail_ci_if_error: false
  
  # 3. BUILD VALIDATION
  build:
    name: Build Validation
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci --ignore-scripts
          cd backend && npm ci
      
      - name: Build Frontend
        run: npm run build
      
      - name: Build Backend
        run: cd backend && npm run build
      
      - name: Check Bundle Size
        run: |
          BUNDLE_SIZE=$(du -sb dist | cut -f1)
          MAX_SIZE=$((10 * 1024 * 1024))  # 10MB
          if [ $BUNDLE_SIZE -gt $MAX_SIZE ]; then
            echo "Bundle size ($BUNDLE_SIZE bytes) exceeds limit ($MAX_SIZE bytes)"
            exit 1
          fi
          echo "Bundle size: $BUNDLE_SIZE bytes"
  
  # 4. DOCKER BUILD
  docker-build:
    name: Docker Build
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    strategy:
      matrix:
        service: [frontend, backend, ml]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Build Docker Image
        run: |
          docker compose -f docker-compose.staging.yml build ${{ matrix.service }}
      
      - name: Test Docker Image
        run: |
          docker compose -f docker-compose.staging.yml run --rm ${{ matrix.service }} --version || true
  
  # 5. INTEGRATION TESTS
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    needs: [docker-build]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: spheroseg_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Start Services
        run: |
          docker compose -f docker-compose.staging.yml up -d
          sleep 30  # Wait for services to be ready
      
      - name: Health Check
        run: |
          curl -f http://localhost:4001/api/health || exit 1
      
      - name: Run API Tests
        run: |
          cd backend
          npm run test:integration
      
      - name: Stop Services
        if: always()
        run: docker compose -f docker-compose.staging.yml down
  
  # 6. E2E TESTS
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    needs: [build]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci --ignore-scripts
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Start Application
        run: |
          docker compose -f docker-compose.staging.yml up -d
          npx wait-on http://localhost:4000 -t 60000
      
      - name: Run E2E Tests
        run: npm run test:e2e
      
      - name: Upload Playwright Report
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
      
      - name: Stop Application
        if: always()
        run: docker compose -f docker-compose.staging.yml down
  
  # 7. SECURITY SCAN
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci --ignore-scripts
          cd backend && npm ci
      
      - name: Run npm audit
        run: |
          npm audit --audit-level=critical
          cd backend && npm audit --audit-level=critical
      
      - name: Run Security Scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          severity: 'CRITICAL,HIGH'
          exit-code: '1'
      
      - name: Check for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.pull_request.base.sha }}
          head: ${{ github.event.pull_request.head.sha }}
  
  # 8. DATABASE MIGRATION CHECK
  database-check:
    name: Database Migration Check
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: spheroseg_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: cd backend && npm ci
      
      - name: Check Prisma Schema
        run: cd backend && npx prisma validate
      
      - name: Run Migrations
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/spheroseg_test
        run: |
          cd backend
          npx prisma migrate deploy
          npx prisma db seed || true
  
  # FINAL STATUS CHECK
  merge-ready:
    name: Merge Ready
    runs-on: ubuntu-latest
    needs: 
      - code-quality
      - unit-tests
      - build
      - docker-build
      - integration-tests
      - e2e-tests
      - security
      - database-check
    if: always()
    
    steps:
      - name: Check Status
        run: |
          if [ "${{ contains(needs.*.result, 'failure') }}" == "true" ]; then
            echo "❌ Some checks failed. PR cannot be merged."
            exit 1
          elif [ "${{ contains(needs.*.result, 'cancelled') }}" == "true" ]; then
            echo "⚠️ Some checks were cancelled."
            exit 1
          else
            echo "✅ All checks passed! PR is ready to merge."
          fi
      
      - name: Add Comment to PR
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '✅ **All pre-merge checks passed!**\n\nThis PR is ready to be merged.'
            })
      
      - name: Add Failure Comment to PR
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '❌ **Pre-merge checks failed!**\n\nPlease fix the failing checks before merging.'
            })