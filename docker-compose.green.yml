version: '3.8'

services:
  # Green Frontend (port 5000)
  green-frontend:
    build:
      context: .
      dockerfile: docker/frontend.prod.Dockerfile
      args:
        - VITE_API_BASE_URL=https://spherosegapp.utia.cas.cz/api
        - VITE_ML_SERVICE_URL=https://spherosegapp.utia.cas.cz/api/ml
        - VITE_WS_URL=wss://spherosegapp.utia.cas.cz
    container_name: green-frontend
    ports:
      - "5000:80"
    networks:
      - green-network
    environment:
      - NODE_ENV=production
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Green Backend API (port 5001)
  green-backend:
    build:
      context: ./backend
      dockerfile: ../docker/backend.prod.Dockerfile
    container_name: green-backend
    ports:
      - "5001:3001"
    networks:
      - green-network
    depends_on:
      postgres-green:
        condition: service_healthy
      redis-green:
        condition: service_healthy
    env_file: .env.green
    environment:
      - NODE_ENV=production
      - HOST=0.0.0.0
      - API_BASE_URL=https://spherosegapp.utia.cas.cz
      - DATABASE_URL=postgresql://spheroseg:${DB_PASSWORD}@postgres-green:5432/spheroseg_green
      - REDIS_URL=redis://redis-green:6379
      - JWT_ACCESS_SECRET=1c4fb341de6e2812f81b0cd5287652d38518287b64289fb21346305bba6fe87d
      - JWT_REFRESH_SECRET=fb5a22af8446e7ac670ea277715ec4bdf9e233efd55f5ddac749edd8f2e9562b
      - FROM_EMAIL=${FROM_EMAIL:-<EMAIL>}
      - SEGMENTATION_SERVICE_URL=http://green-ml:8000
      - CORS_ORIGIN=https://spherosegapp.utia.cas.cz,http://***************:5000,http://localhost:5000
      - WS_ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz,wss://spherosegapp.utia.cas.cz,http://***************:5000,http://localhost:5000
      - ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz,http://***************:5000,http://localhost:5000
      - FRONTEND_URL=https://spherosegapp.utia.cas.cz
      # Email configuration - uses environment variables
      - EMAIL_SERVICE=${EMAIL_SERVICE:-smtp}
      - SMTP_HOST=${SMTP_HOST:-localhost}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_SECURE=${SMTP_SECURE:-false}
      - SMTP_AUTH=${SMTP_AUTH:-true}
      - SMTP_REQUIRE_TLS=${SMTP_REQUIRE_TLS:-true}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - SKIP_EMAIL_SEND=${SKIP_EMAIL_SEND:-false}
      - EMAIL_ALLOW_INSECURE=${EMAIL_ALLOW_INSECURE:-false}
      - EMAIL_TIMEOUT=${EMAIL_TIMEOUT:-60000}
    volumes:
      - ./backend/uploads/green:/app/uploads
      - green-backend-logs:/app/logs
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Green PostgreSQL Database
  postgres-green:
    image: postgres:15-alpine
    container_name: postgres-green
    networks:
      - green-network
    environment:
      - POSTGRES_USER=spheroseg
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=spheroseg_green
    volumes:
      - green-postgres-data:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Green Redis Cache
  redis-green:
    image: redis:7-alpine
    container_name: redis-green
    networks:
      - green-network
    volumes:
      - green-redis-data:/data
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Green ML Service (port 5008) with GPU support
  green-ml:
    build:
      context: ./backend/segmentation
      dockerfile: ../../docker/ml.Dockerfile
    container_name: green-ml
    ports:
      - "5008:8000"
    networks:
      - green-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
        limits:
          memory: 8G
    environment:
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=production
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_DEVICE_ORDER=PCI_BUS_ID
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
    volumes:
      - ./backend/segmentation/weights:/app/weights
      - ./backend/uploads/green:/app/uploads
      - green-ml-logs:/app/logs
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 20s
      retries: 5
      start_period: 120s

  # Nginx Reverse Proxy (using unified config)
  nginx-green:
    image: nginx:alpine
    container_name: nginx-green
    volumes:
      - ./docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - green-frontend
      - green-backend
      - green-ml
    networks:
      - green-network
    restart: always
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  green-network:
    driver: bridge

volumes:
  green-postgres-data:
  green-redis-data:
  green-backend-logs:
  green-ml-logs: