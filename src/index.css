@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply h-full;
  }

  body {
    @apply bg-background text-foreground h-full;
    font-family:
      'Inter',
      ui-sans-serif,
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      'Noto Sans',
      sans-serif;
    font-feature-settings:
      'rlig' 1,
      'calt' 1,
      'ss01' 1;
    font-optical-sizing: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  #root {
    @apply h-full;
  }

  /* Dark mode overrides - applied globally */
  .dark {
    @apply text-white;
  }

  .dark body {
    @apply bg-gray-900;
  }

  .dark .bg-gray-50 {
    @apply bg-gray-900;
  }

  .dark .bg-white {
    @apply bg-gray-800;
  }

  .dark .border-gray-200 {
    @apply border-gray-700;
  }

  .dark .text-gray-500 {
    @apply text-gray-400;
  }

  .dark .text-gray-700 {
    @apply text-gray-300;
  }

  .dark .hover\:bg-gray-50:hover {
    @apply hover:bg-gray-800;
  }
}

/* Custom utility classes */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Consistent dark theme background for all pages */
  .dark .min-h-screen {
    @apply bg-gray-900;
  }

  /* Better contrast for text in dark mode */
  .dark .text-gray-900 {
    @apply text-gray-100;
  }

  /* Optimized polygon rendering styles */
  .polygon-path {
    image-rendering: crisp-edges;
    shape-rendering: geometricPrecision;
    pointer-events: visiblePainted;
  }

  .polygon-external {
    fill: rgba(239, 68, 68, 0.15);
  }

  .polygon-internal {
    fill: rgba(14, 165, 233, 0.15);
  }

  .polygon-selected {
    filter: drop-shadow(0 0 8px var(--polygon-selected-glow, #3b82f6));
  }

  /* Performance optimizations for large datasets */
  .polygon-collection {
    will-change: transform;
    contain: layout style paint;
  }

  .polygon-vertex {
    will-change: transform;
    contain: strict;
  }
}
