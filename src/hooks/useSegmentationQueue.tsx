import { logger } from '@/lib/logger';
import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '@/contexts/useAuth';
import { useWebSocket } from '@/contexts/useWebSocket';
import { useLanguage } from '@/contexts/useLanguage';
import { toast } from 'sonner';
import WebSocketManager from '@/services/webSocketManager';
import type {
  QueueStats,
  SegmentationUpdate,
  SegmentationStatusMessage,
  QueueStatsMessage,
  SegmentationCompletedMessage,
  SegmentationFailedMessage,
  WebSocketEventMap,
} from '@/types/websocket';

export type { QueueStats, SegmentationUpdate } from '@/types/websocket';

export const useSegmentationQueue = (projectId?: string) => {
  // Check if this hook should be disabled to avoid conflicts
  const isDisabled = projectId === 'DISABLE_GLOBAL';

  const { user, token } = useAuth();
  const { manager: contextManager, isConnected: contextIsConnected } =
    useWebSocket();
  const { t } = useLanguage();
  const wsManagerRef = useRef<WebSocketManager | null>(null);
  const currentProjectRef = useRef<string | undefined>(
    isDisabled ? undefined : projectId
  );
  const isInitializedRef = useRef(false);
  const [isConnected, setIsConnected] = useState(false);
  const [queueStats, setQueueStats] = useState<QueueStats | null>(null);
  const [lastUpdate, setLastUpdate] = useState<SegmentationUpdate | null>(null);

  // Store t function in ref to avoid dependency issues
  const tRef = useRef(t);
  tRef.current = t;

  // Create stable callback that has access to current t function
  const handleSegmentationUpdate = useCallback((update: SegmentationUpdate) => {
    setLastUpdate(update);

    // Show toast notifications for status changes
    if (update.status === 'segmented') {
      toast.success(
        tRef.current('toast.segmentation.completed') ||
          tRef.current('projects.segmentationCompleted')
      );
    } else if (update.status === 'no_segmentation') {
      toast.warning(
        tRef.current('toast.segmentation.noPolygons') ||
          'No segmentation polygons detected'
      );
    } else if (update.status === 'failed') {
      const errorMessage = update.error || tRef.current('errors.unknown');
      toast.error(
        `${tRef.current('toast.segmentation.failed') || tRef.current('projects.segmentationFailed')}: ${errorMessage}`
      );
    } else if (update.status === 'processing') {
      toast.info(
        tRef.current('toast.segmentation.started') ||
          tRef.current('projects.segmentationStarted')
      );
    }
  }, []); // No dependencies

  const handleQueueStatsUpdate = useCallback((stats: QueueStats) => {
    if (
      !currentProjectRef.current ||
      stats.projectId === currentProjectRef.current
    ) {
      setQueueStats(stats);
    }
  }, []);

  const handleNotification = useCallback((notification: Notification) => {
    if (notification.type === 'segmentation-complete') {
      toast.success(
        tRef.current('toast.segmentation.completedWithCount', {
          count: notification.polygonCount,
        }) ||
          tRef.current('projects.segmentationCompleteWithCount', {
            count: notification.polygonCount,
          }),
        { duration: 5000 }
      );
    }
  }, []); // No dependencies

  const handleSystemMessage = useCallback((message: SystemMessage) => {
    if (message.type === 'warning') {
      toast.warning(message.message);
    } else if (message.type === 'error') {
      toast.error(message.message);
    } else {
      toast.info(message.message);
    }
  }, []);

  // Update current project reference when projectId changes
  useEffect(() => {
    currentProjectRef.current = isDisabled ? undefined : projectId;
  }, [projectId, isDisabled]);

  // Update connection status based on context and request initial stats
  useEffect(() => {
    setIsConnected(contextIsConnected);

    // When connection is established, immediately request queue stats
    if (
      contextIsConnected &&
      wsManagerRef.current &&
      projectId &&
      !isDisabled
    ) {
      if (process.env.NODE_ENV === 'development') {
        logger.debug(
          'Connection established, requesting initial queue stats for project:',
          projectId
        );
      }
      wsManagerRef.current.requestQueueStats(projectId);
    }
  }, [contextIsConnected, projectId, isDisabled]);

  // Initialize event listeners - use context manager if available, fallback to singleton
  useEffect(() => {
    // Don't setup event listeners if this hook is disabled
    if (isDisabled) {
      return;
    }

    if (process.env.NODE_ENV === 'development') {
      logger.debug('useSegmentationQueue - setting up event listeners');
    }

    if (!user || !token) {
      if (process.env.NODE_ENV === 'development') {
        logger.debug(
          'useSegmentationQueue - no auth, cleaning up event listeners'
        );
      }
      if (wsManagerRef.current) {
        const manager = wsManagerRef.current;
        manager.off('segmentation-update', handleSegmentationUpdate);
        manager.off('queue-stats-update', handleQueueStatsUpdate);
        manager.off('notification', handleNotification);
        manager.off('system-message', handleSystemMessage);
        wsManagerRef.current = null;
      }
      return;
    }

    // Use context manager if available, otherwise get singleton instance
    const manager = contextManager || WebSocketManager.getInstance();
    wsManagerRef.current = manager;

    // Register event listeners
    manager.on('segmentation-update', handleSegmentationUpdate);
    manager.on('queue-stats-update', handleQueueStatsUpdate);
    manager.on('notification', handleNotification);
    manager.on('system-message', handleSystemMessage);

    if (process.env.NODE_ENV === 'development') {
      logger.debug('useSegmentationQueue - event listeners registered');
    }

    // Cleanup function - only unregister listeners
    return () => {
      if (process.env.NODE_ENV === 'development') {
        logger.debug('useSegmentationQueue - cleaning up event listeners');
      }

      if (manager) {
        manager.off('segmentation-update', handleSegmentationUpdate);
        manager.off('queue-stats-update', handleQueueStatsUpdate);
        manager.off('notification', handleNotification);
        manager.off('system-message', handleSystemMessage);
      }
    };
  }, [
    user,
    token,
    contextManager,
    isDisabled,
    handleSegmentationUpdate,
    handleQueueStatsUpdate,
    handleNotification,
    handleSystemMessage,
  ]);

  // Join project room when projectId changes and connection is ready
  useEffect(() => {
    if (isDisabled || !wsManagerRef.current || !isConnected || !projectId) {
      return;
    }

    if (process.env.NODE_ENV === 'development') {
      logger.debug('Joining project room:', projectId);
    }
    wsManagerRef.current.joinProject(projectId);

    // Request queue stats immediately and set up periodic refresh
    wsManagerRef.current.requestQueueStats(projectId);

    // Request queue stats every 5 seconds to ensure we have fresh data
    const intervalId = setInterval(() => {
      if (wsManagerRef.current && isConnected && projectId) {
        wsManagerRef.current.requestQueueStats(projectId);
      }
    }, 5000);

    return () => {
      clearInterval(intervalId);
    };
  }, [projectId, isConnected, isDisabled]);

  // Functions for interacting with the queue
  const requestQueueStats = useCallback(() => {
    if (wsManagerRef.current && isConnected && projectId) {
      wsManagerRef.current.requestQueueStats(projectId);
    }
  }, [isConnected, projectId]);

  const joinProject = useCallback(
    (newProjectId: string) => {
      if (wsManagerRef.current && isConnected) {
        // Leave current project if any
        if (currentProjectRef.current) {
          wsManagerRef.current.leaveProject(currentProjectRef.current);
        }

        // Update ref first to prevent race condition
        currentProjectRef.current = newProjectId;

        // Then join new project and request stats
        wsManagerRef.current.joinProject(newProjectId);
        wsManagerRef.current.requestQueueStats(newProjectId);
      }
    },
    [isConnected]
  );

  const leaveProject = useCallback(() => {
    if (wsManagerRef.current && isConnected && currentProjectRef.current) {
      wsManagerRef.current.leaveProject(currentProjectRef.current);
      setQueueStats(null);
      currentProjectRef.current = undefined;
    }
  }, [isConnected]);

  return {
    isConnected,
    queueStats,
    lastUpdate,
    requestQueueStats,
    joinProject,
    leaveProject,
  };
};
