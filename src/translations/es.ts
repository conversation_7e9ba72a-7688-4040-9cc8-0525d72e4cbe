export default {
  common: {
    appName: 'Segmentación de Esferoides',
    loading: 'Cargando...',
    save: '<PERSON><PERSON>',
    cancel: 'Cancelar',
    delete: 'Eliminar',
    edit: 'Editar',
    create: 'Crear',
    search: '<PERSON><PERSON>',
    error: 'Error',
    success: '<PERSON>xito',
    back: 'Volver',
    signIn: 'Iniciar sesión',
    signUp: 'Registrar<PERSON>',
    signOut: 'Cerrar sesión',
    settings: 'Configuración',
    profile: 'Perfil',
    dashboard: 'Panel de control',
    project: 'Proyecto',
    projects: 'Proyectos',
    polygon: 'Polígono',
    newProject: 'Nuevo proyecto',
    upload: 'Subir',
    uploadImages: 'Subir imágenes',
    recentAnalyses: 'Análisis recientes',
    noProjects: 'No se encontraron proyectos',
    noImages: 'No se encontraron imágenes',
    createYourFirst: 'Crea tu primer proyecto para comenzar',
    tryAgain: 'Intentar de nuevo',
    email: 'Correo electr<PERSON>',
    password: '<PERSON><PERSON><PERSON><PERSON>',
    name: '<PERSON><PERSON>',
    description: '<PERSON><PERSON><PERSON><PERSON>',
    date: '<PERSON><PERSON>',
    status: 'Estado',
    images: 'Imá<PERSON>s',
    image: 'Imagen',
    projectName: 'Nombre del proyecto',
    projectDescription: 'Descripción del proyecto',
    theme: 'Tema',
    language: 'Idioma',
    light: 'Claro',
    dark: 'Oscuro',
    system: 'Sistema',
    welcome: 'Bienvenido a la plataforma de segmentación de esferoides',
    account: 'Cuenta',
    notifications: 'Notificaciones',
    passwordConfirm: 'Confirmar contraseña',
    manageAccount: 'Administrar tu cuenta',
    documentation: 'Documentación',
    changePassword: 'Cambiar contraseña',
    deleteAccount: 'Eliminar cuenta',
    termsOfService: 'Términos de servicio',
    privacyPolicy: 'Política de privacidad',
    createAccount: 'Crear cuenta',
    signInToAccount: 'Iniciar sesión en tu cuenta',
    sort: 'Ordenar',
    no_preview: 'Sin vista previa',
    // Navigation and UI
    openMenu: 'Abrir menú',
    logOut: 'Cerrar sesión',
    // Error pages
    pageNotFound: '¡Ups! Página no encontrada',
    returnToHome: 'Volver al inicio',
    // Navigation
    next: 'Siguiente',
  },
  dashboard: {
    manageProjects: 'Administra tus proyectos de investigación y análisis',
    projectGallery: 'Galería de Proyectos',
    projectGalleryDescription:
      'Explora y administra todos tus proyectos de segmentación',
    statsOverview: 'Resumen de estadísticas',
    totalProjects: 'Total de proyectos',
    activeProjects: 'Proyectos activos',
    totalImages: 'Total de imágenes',
    totalAnalyses: 'Total de análisis',
    lastUpdated: 'Última actualización',
    noProjectsDescription:
      'Aún no has creado ningún proyecto. Crea tu primer proyecto para comenzar.',
    noImagesDescription: 'Sube algunas imágenes para comenzar',
    searchProjectsPlaceholder: 'Buscar proyectos...',
    searchImagesPlaceholder: 'Buscar imágenes por nombre...',
    sortBy: 'Ordenar por',
    name: 'Nombre',
    lastChange: 'Último cambio',
    status: 'Estado',
    // Stats overview
    stats: {
      totalProjects: 'Total de proyectos',
      totalProjectsDesc: 'Estudios activos de esferoides',
      processedImages: 'Imágenes procesadas',
      processedImagesDesc: 'Segmentadas exitosamente',
      uploadedToday: 'Subidas hoy',
      uploadedTodayDesc: 'Imágenes de esferoides',
      storageUsed: 'Almacenamiento usado',
      totalSpaceUsed: 'Espacio total usado',
    },
    completed: 'Completado',
    processing: 'Procesando',
    pending: 'Pendiente',
    failed: 'Fallido',
    storageUsed: 'Almacenamiento Usado',
  },
  projects: {
    createProject: 'Crear nuevo proyecto',
    createProjectDesc:
      'Añade un nuevo proyecto para organizar tus imágenes y análisis de esferoides.',
    projectNamePlaceholder: 'ej. Esferoides de células HeLa',
    projectDescPlaceholder:
      'ej. Análisis de esferoides tumorales para estudios de resistencia a fármacos',
    creatingProject: 'Creando...',
    duplicateProject: 'Duplicar',
    shareProject: 'Compartir',
    deleteProject: 'Eliminar',
    openProject: 'Abrir proyecto',
    confirmDelete: '¿Estás seguro de que quieres eliminar este proyecto?',
    projectCreated: 'Proyecto creado con éxito',
    projectDeleted: 'Proyecto eliminado con éxito',
    viewProject: 'Ver proyecto',
    projectImages: 'Imágenes del proyecto',
    noProjects: 'No se encontraron proyectos',
    imageDeleted: 'Imagen eliminada con éxito',
    deleteImageError: 'Error al eliminar imagen',
    deleteImageFailed: 'Falló la eliminación de imagen',
    imagesQueuedForSegmentation:
      '{{count}} imágenes añadidas a la cola de segmentación',
    allImagesAlreadySegmented:
      'Todas las imágenes ya están segmentadas o en cola',
    errorAddingToQueue: 'Error al añadir imágenes a la cola',
    imageAlreadyProcessing: 'La imagen ya está siendo procesada',
    processImageFailed: 'Error al procesar la imagen',
    segmentationCompleted: 'Segmentación completada para la imagen',
    segmentationFailed: 'La segmentación ha fallado',
    segmentationStarted: 'La segmentación ha comenzado',
    segmentationCompleteWithCount:
      'Segmentación completa! Se encontraron {{count}} objetos',
    // Project management errors and messages
    failedToLoadProjects: 'Error al cargar proyectos',
    projectNameRequired: 'Por favor ingrese un nombre de proyecto',
    mustBeLoggedIn: 'Debe estar conectado para crear un proyecto',
    failedToCreateProject: 'Error al crear proyecto',
    serverResponseInvalid: 'La respuesta del servidor fue inválida',
    projectCreatedDesc: '"{{name}}" está listo para imágenes',
    descriptionOptional: 'Descripción (Opcional)',
    noDescriptionProvided: 'No se proporcionó descripción',
    selectProject: 'Seleccionar Proyecto',
    projectSelection: 'Selección de Proyecto',
    selectProjectHeader: 'Seleccionar Proyecto',
  },
  errors: {
    noProjectOrUser: 'Error: No hay proyecto o usuario seleccionado',
    unknown: 'Error desconocido',
    validation: {
      projectNameRequired: 'Por favor ingrese un nombre de proyecto',
      loginRequired: 'Debe estar conectado para crear un proyecto',
    },
  },
  images: {
    uploadImages: 'Subir imágenes',
    dragDrop: 'Arrastra y suelta imágenes aquí',
    clickToSelect: 'o haz clic para seleccionar archivos',
    acceptedFormats: 'Formatos aceptados: JPEG, PNG, TIFF, BMP (máx. 10MB)',
    uploadProgress: 'Progreso de la carga',
    uploadingTo: 'Subiendo a',
    currentProject: 'Proyecto actual',
    autoSegment: 'Segmentar automáticamente las imágenes después de la carga',
    uploadCompleted: 'Carga completada',
    uploadFailed: 'Carga fallida',
    imagesUploaded: 'Imágenes subidas con éxito',
    imagesFailed: 'Error al subir imágenes',
    viewAnalyses: 'Ver análisis',
    noAnalysesYet: 'Aún no hay análisis',
    runAnalysis: 'Ejecutar análisis',
    viewResults: 'Ver resultados',
    dropImagesHere: 'Suelta las imágenes aquí...',
    selectProjectFirst: 'Por favor selecciona un proyecto primero',
    projectRequired:
      'Debes seleccionar un proyecto antes de poder subir imágenes',
    pending: 'Pendiente',
    uploading: 'Subiendo',
    processing: 'Procesando',
    complete: 'Completo',
    error: 'Error',
    imageDeleted: 'Imagen eliminada con éxito',
    deleteImageFailed: 'Error al eliminar imagen',
    deleteImageError: 'Error al eliminar imagen',
    imageAlreadyProcessing: 'La imagen ya está siendo procesada',
    processImageFailed: 'Error al procesar la imagen',
  },
  settings: {
    manageSettings: 'Administra las preferencias de tu cuenta',
    appearance: 'Apariencia',
    themeSettings: 'Configuración del tema',
    systemDefault: 'Predeterminado del sistema',
    languageSettings: 'Configuración de idioma',
    selectLanguage: 'Seleccionar idioma',
    accountSettings: 'Configuración de la cuenta',
    notificationSettings: 'Configuración de notificaciones',
    emailNotifications: 'Notificaciones por correo electrónico',
    pushNotifications: 'Notificaciones push',
    profileSettings: 'Configuración del perfil',
    profileUpdated: 'Perfil actualizado con éxito',
    profileUpdateFailed: 'Error al actualizar el perfil',
    saveChanges: 'Guardar cambios',
    savingChanges: 'Guardando cambios...',
    notifications: {
      projectUpdates: 'Actualizaciones de proyectos',
      analysisCompleted: 'Análisis completado',
      newFeatures: 'Nuevas características',
      marketingEmails: 'Correos de marketing',
      billing: 'Notificaciones de facturación',
    },
    models: 'Modelos',
    modelSelection: {
      title: 'Selección de modelo',
      description: 'Elige el modelo de IA para usar en la segmentación celular',
      models: {
        hrnet: {
          name: 'HRNet',
          description:
            'Modelo rápido y eficiente para segmentación en tiempo real',
        },
        cbam: {
          name: 'CBAM-ResUNet',
          description:
            'Velocidad y precisión equilibradas para la mayoría de casos de uso',
        },
        ma: {
          name: 'MA-ResUNet',
          description: 'Mayor precisión con mecanismos de atención',
        },
      },
    },
    confidenceThreshold: 'Umbral de Confianza',
    confidenceThresholdDescription:
      'Confianza mínima requerida para las predicciones de segmentación',
    currentThreshold: 'Umbral actual',
    modelSelected: 'Modelo seleccionado con éxito',
    modelSettingsSaved: 'Configuración de modelo guardada con éxito',
    modelSize: {
      small: 'Pequeño',
      medium: 'Mediano',
      large: 'Grande',
    },
    modelDescription: {
      hrnet: 'Modelo rápido y eficiente para segmentación en tiempo real',
      resunet_small:
        'Velocidad y precisión equilibradas para la mayoría de casos de uso',
      resunet_advanced: 'Mayor precisión con mecanismos de atención',
    },
    dataUsageTitle: 'Uso de datos y privacidad',
    dataUsageDescription:
      'Controla cómo se utilizan tus datos para el aprendizaje automático y la investigación',
    allowMLTraining: {
      label: 'Permitir entrenamiento de modelos ML',
      description:
        'Permitir que tus datos se utilicen para entrenar y mejorar nuestros modelos de segmentación',
    },
    cancel: 'Cancelar',
    deleting: 'Eliminando...',
    deleteAccount: 'Eliminar Cuenta',
    accountDeleted: 'Cuenta eliminada con éxito',
    deleteAccountError: 'Error al eliminar la cuenta',
    deleteAccountDialog: {
      title: 'Eliminar cuenta',
      description:
        'Esta acción no se puede deshacer. Esto eliminará permanentemente tu cuenta y removerá todos tus datos de nuestros servidores.',
      whatWillBeDeleted: 'Qué será eliminado:',
      deleteItems: {
        account: 'Tu cuenta de usuario y perfil',
        projects: 'Todos tus proyectos e imágenes',
        segmentation: 'Todos los datos de segmentación y resultados',
        settings: 'Configuración de cuenta y preferencias',
      },
      confirmationLabel: 'Por favor escribe {email} para confirmar:',
      confirmationPlaceholder: '{email}',
    },
    pageTitle: 'Configuración',
    profile: 'Perfil',
    account: 'Cuenta',
    personal: 'Información Personal',
    fullName: 'Nombre Completo',
    organization: 'Organización',
    department: 'Departamento',
    publicProfile: 'Perfil Público',
    bio: 'Biografía',
    makeProfileVisible: 'Hacer mi perfil visible para otros investigadores',
    dangerZone: 'Zona de Peligro',
    deleteAccountWarning:
      'Una vez que elimines tu cuenta, no hay vuelta atrás. Todos tus datos serán eliminados permanentemente.',
    currentPassword: 'Contraseña Actual',
    newPassword: 'Nueva Contraseña',
    confirmNewPassword: 'Confirmar Nueva Contraseña',
    fillAllFields: 'Por favor completa todos los campos requeridos',
    passwordsDoNotMatch: 'Las contraseñas no coinciden',
    passwordTooShort: 'La contraseña debe tener al menos 6 caracteres',
    passwordChanged: 'Contraseña cambiada con éxito',
    passwordsMatch: 'Las contraseñas coinciden',
    changingPassword: 'Cambiando contraseña...',
    changePassword: 'Cambiar Contraseña',
    languageUpdated: 'Idioma actualizado con éxito',
    themeUpdated: 'Tema actualizado con éxito',
    appearanceDescription: 'Personaliza la apariencia de la aplicación',
    language: 'Idioma',
    languageDescription: 'Selecciona tu idioma preferido',
    theme: 'Tema',
    themeDescription: 'Elige tema claro, oscuro o del sistema',
    light: 'Claro',
    dark: 'Oscuro',
    system: 'Sistema',
  },
  auth: {
    signIn: 'Iniciar sesión',
    signUp: 'Registrarse',
    signOut: 'Cerrar sesión',
    forgotPassword: '¿Olvidaste tu contraseña?',
    resetPassword: 'Restablecer contraseña',
    dontHaveAccount: '¿No tienes una cuenta?',
    alreadyHaveAccount: '¿Ya tienes una cuenta?',
    signInWith: 'Iniciar sesión con',
    signUpWith: 'Registrarse con',
    orContinueWith: 'o continuar con',
    rememberMe: 'Recordarme',
    emailRequired: 'El correo electrónico es obligatorio',
    passwordRequired: 'La contraseña es obligatoria',
    invalidEmail: 'Dirección de correo electrónico no válida',
    passwordTooShort: 'La contraseña debe tener al menos 6 caracteres',
    passwordsDontMatch: 'Las contraseñas no coinciden',
    successfulSignIn: 'Inicio de sesión exitoso',
    successfulSignUp: 'Registro exitoso',
    verifyEmail:
      'Por favor, verifica tu correo electrónico para confirmar tu cuenta',
    successfulSignOut: 'Cierre de sesión exitoso',
    checkingAuthentication: 'Verificando autenticación...',
    loadingAccount: 'Cargando tu cuenta...',
    processingRequest: 'Procesando tu solicitud...',
    // SignIn page specific
    signInToAccount: 'Inicia sesión en tu cuenta',
    accessPlatform: 'Accede a la plataforma de segmentación de esferoides',
    emailAddress: 'Dirección de correo electrónico',
    emailPlaceholder: '<EMAIL>',
    password: 'Contraseña',
    passwordPlaceholder: '••••••••',
    signingIn: 'Iniciando sesión...',
    redirectingToDashboard: 'Redirigiendo al panel...',
    fillAllFields: 'Por favor, completa todos los campos',
    // Toast messages
    signInSuccess: 'Inicio de sesión exitoso',
    signInFailed: 'Error en el inicio de sesión',
    registrationSuccess: 'Registro exitoso',
    registrationFailed: 'Error en el registro',
    logoutFailed: 'Error al cerrar sesión',
    profileUpdateFailed: 'Error al actualizar el perfil',
    welcomeMessage: 'Bienvenido a la plataforma de segmentación de esferoides',
    confirmationRequired:
      'El texto de confirmación es obligatorio y debe coincidir con tu dirección de correo electrónico',
    agreeToTerms: 'Al iniciar sesión, aceptas nuestros',
    termsOfService: 'Términos de Servicio',
    and: 'y',
    privacyPolicy: 'Política de Privacidad',
    // SignUp page specific
    createAccount: 'Crea tu cuenta',
    signUpPlatform:
      'Regístrate para usar la plataforma de segmentación de esferoides',
    confirmPassword: 'Confirmar contraseña',
    passwordsMatch: 'Las contraseñas coinciden',
    passwordsDoNotMatch: 'Las contraseñas no coinciden',
    agreeToTermsCheckbox: 'Acepto los',
    mustAgreeToTerms: 'Debes aceptar los términos y condiciones',
    creatingAccount: 'Creando cuenta...',
    alreadyLoggedIn: 'Ya has iniciado sesión',
    alreadySignedUp: 'Ya te has registrado e iniciado sesión.',
    goToDashboard: 'Ir al Panel',
    signUpFailed: 'Error en el registro',
  },
  profile: {
    title: 'Perfil',
    about: 'Acerca de',
    activity: 'Actividad',
    projects: 'Proyectos',
    papers: 'Artículos',
    analyses: 'Análisis',
    recentProjects: 'Proyectos recientes',
    recentAnalyses: 'Análisis recientes',
    accountDetails: 'Detalles de cuenta',
    accountType: 'Tipo de cuenta',
    joinDate: 'Fecha de registro',
    lastActive: 'Última actividad',
    projectsCreated: 'Proyectos creados',
    imagesUploaded: 'Imágenes subidas',
    segmentationsCompleted: 'Segmentaciones completadas',
    editProfile: 'Editar perfil',
    joined: 'Unido',
    copyApiKey: 'Copiar clave API',
    collaborators: 'Colaboradores',
    noCollaborators: 'Sin colaboradores',
    connectedAccounts: 'Cuentas conectadas',
    connect: 'Conectar',
    recentActivity: 'Actividad reciente',
    noRecentActivity: 'Sin actividad reciente',
    statistics: 'Estadísticas',
    totalImagesProcessed: 'Total de imágenes procesadas',
    averageProcessingTime: 'Tiempo promedio de procesamiento',
    fromLastMonth: 'desde el mes pasado',
    storageUsed: 'Almacenamiento usado',
    of: 'de',
    apiRequests: 'Solicitudes API',
    thisMonth: 'este mes',
    recentPublications: 'Publicaciones recientes',
    viewAll: 'Ver todo',
    noPublications: 'Aún no hay publicaciones',
    today: 'hoy',
    yesterday: 'ayer',
    daysAgo: 'días atrás',
    completionRate: 'tasa de finalización',
    createdProject: 'Creó proyecto',
    completedSegmentation: 'Completó segmentación para',
    uploadedImage: 'Subió imagen',
  },
  segmentation: {
    mode: {
      view: 'Ver y navegar',
      edit: 'Editar',
      editVertices: 'Editar vértices',
      addPoints: 'Añadir puntos',
      create: 'Crear',
      createPolygon: 'Crear polígono',
      slice: 'Cortar',
      delete: 'Eliminar',
      deletePolygon: 'Eliminar polígono',
      unknown: 'Desconocido',
    },
    modeDescription: {
      view: 'Navegar y seleccionar polígonos',
      edit: 'Mover y modificar vértices',
      addPoints: 'Añadir puntos entre vértices',
      create: 'Crear nuevos polígonos',
      slice: 'Dividir polígonos con una línea',
      delete: 'Eliminar polígonos',
    },
    toolbar: {
      mode: 'Modo',
      keyboard: 'Tecla: {{key}}',
      requiresSelection: 'Requiere selección de polígono',
      requiresPolygonSelection: 'Requiere selección de polígono',
      select: 'Seleccionar',
      undoTooltip: 'Deshacer (Ctrl+Z)',
      undo: 'Deshacer',
      redoTooltip: 'Rehacer (Ctrl+Y)',
      redo: 'Rehacer',
      zoomInTooltip: 'Acercar (+)',
      zoomIn: 'Acercar',
      zoomOutTooltip: 'Alejar (-)',
      zoomOut: 'Alejar',
      resetViewTooltip: 'Restablecer vista (R)',
      resetView: 'Restablecer',
      unsavedChanges: 'Cambios no guardados',
      saving: 'Guardando...',
      save: 'Guardar',
      keyboardShortcuts:
        'V: Ver • E: Editar • A: Añadir • N: Nuevo • S: Cortar • D: Eliminar',
      nothingToSave: 'Todos los cambios guardados',
    },
    status: {
      polygons: 'polígonos',
      vertices: 'vértices',
      visible: 'visibles',
      hidden: 'ocultos',
      selected: 'seleccionado',
      saved: 'Guardado',
      unsaved: 'No guardado',
      noPolygons: 'Sin polígonos',
      startCreating: 'Comience creando un polígono',
      polygonList: 'Lista de Polígonos',
    },
    shortcuts: {
      buttonText: 'Atajos',
      dialogTitle: 'Atajos de teclado',
      footerNote:
        'Estos atajos funcionan dentro del editor de segmentación para un trabajo más rápido y conveniente.',
      v: 'Modo vista',
      e: 'Modo editar vértices',
      a: 'Modo agregar puntos',
      n: 'Crear nuevo polígono',
      s: 'Modo cortar',
      d: 'Modo eliminar',
      shift: 'Mantener para adición automática de puntos',
      ctrlZ: 'Deshacer',
      ctrlY: 'Rehacer',
      delete: 'Eliminar polígono seleccionado',
      esc: 'Cancelar operación actual',
      plus: 'Acercar',
      minus: 'Alejar',
      r: 'Restablecer vista',
    },
    tips: {
      header: 'Consejos:',
      edit: {
        createPoint: 'Haz clic para crear un nuevo punto',
        holdShift:
          'Mantén Shift para crear automáticamente secuencia de puntos',
        closePolygon: 'Cierra el polígono haciendo clic en el primer punto',
      },
      slice: {
        startSlice: 'Haz clic para comenzar el corte',
        endSlice: 'Haz clic nuevamente para completar el corte',
        cancelSlice: 'Esc cancela el corte',
      },
      addPoints: {
        hoverLine: 'Sitúa el cursor sobre la línea del polígono',
        clickAdd: 'Haz clic para agregar punto al polígono seleccionado',
        escCancel: 'Esc termina el modo agregar',
      },
    },
    helpTips: {
      editMode: [
        'Haz clic para crear un nuevo punto',
        'Mantén Shift para crear automáticamente secuencia de puntos',
        'Cierra el polígono haciendo clic en el primer punto',
      ],
      slicingMode: [
        'Haz clic para comenzar el corte',
        'Haz clic nuevamente para terminar el corte',
        'Esc cancela el corte',
      ],
      pointAddingMode: [
        'Sitúa el cursor sobre la línea del polígono',
        'Haz clic para agregar punto al polígono seleccionado',
        'Esc sale del modo agregar',
      ],
    },
    loading: 'Cargando segmentación...',
    noPolygons: 'No se encontraron polígonos',
    polygonNotFound: 'Polígono no encontrado',
    invalidSlice: 'Operación de corte inválida',
    sliceSuccess: 'Polígono cortado exitosamente',
    sliceFailed: 'Error al cortar el polígono',
    instructions: {
      slice: {
        selectPolygon:
          '1. Haz clic en un polígono para seleccionarlo para cortar',
        placeFirstPoint: '2. Haz clic para colocar el primer punto de corte',
        placeSecondPoint:
          '3. Haz clic para colocar el segundo punto de corte y realizar el corte',
        cancel: 'Presiona ESC para cancelar',
      },
      create: {
        startPolygon: '1. Haz clic para comenzar a crear un polígono',
        continuePoints:
          '2. Continúa haciendo clic para agregar más puntos (se necesitan al menos 3)',
        finishPolygon:
          '3. Continúa agregando puntos o haz clic cerca del primer punto para cerrar el polígono',
        holdShift: 'Mantén SHIFT para agregar puntos automáticamente',
        cancel: 'Presiona ESC para cancelar',
      },
      addPoints: {
        clickVertex:
          'Haz clic en cualquier vértice para comenzar a agregar puntos',
        addPoints:
          'Haz clic para agregar puntos, luego haz clic en otro vértice para completar. Haz clic directamente en otro vértice sin agregar puntos para eliminar todos los puntos entre ellos.',
        holdShift: 'Mantén SHIFT para agregar puntos automáticamente',
        cancel: 'Presiona ESC para cancelar',
      },
      editVertices: {
        selectPolygon: 'Haz clic en un polígono para seleccionarlo para editar',
        dragVertices: 'Haz clic y arrastra vértices para moverlos',
        addPoints: 'Mantén SHIFT y haz clic en un vértice para agregar puntos',
        deleteVertex: 'Doble clic en un vértice para eliminarlo',
      },
      deletePolygon: {
        clickToDelete: 'Haz clic en un polígono para eliminarlo',
      },
      view: {
        selectPolygon: 'Haz clic en un polígono para seleccionarlo',
        navigation: 'Arrastra para desplazar • Desplaza para acercar',
      },
      modes: {
        slice: 'Modo cortar',
        create: 'Modo crear polígono',
        addPoints: 'Modo agregar puntos',
        editVertices: 'Modo editar vértices',
        deletePolygon: 'Modo eliminar polígono',
        view: 'Modo vista',
      },
      shiftIndicator: '⚡ SHIFT: Agregando puntos automáticamente',
    },
  },
  status: {
    segmented: 'Segmentado',
    processing: 'Procesando',
    queued: 'En cola',
    failed: 'Fallido',
    no_segmentation: 'Sin segmentación',
    disconnected: 'Desconectado del servidor',
    error: 'Error del servicio ML',
    ready: 'Listo para segmentación',
  },
  queue: {
    title: 'Cola de Segmentación',
    connected: 'Conectado',
    disconnected: 'Desconectado',
    waiting: 'esperando',
    processing: 'procesando',
    segmentAll: 'Segmentar Todo',
    totalProgress: 'Progreso Total',
    images: 'imágenes',
    loadingStats: 'Cargando estadísticas...',
    connectingMessage:
      'Conectando al servidor... Las actualizaciones en tiempo real estarán disponibles pronto.',
    emptyMessage:
      'No hay imágenes en cola. Sube imágenes y añádelas a la cola para segmentación.',
    addingToQueue: 'Añadiendo a la cola...',
  },
  toast: {
    // Generic messages
    error: 'Ha ocurrido un error',
    success: 'Operación exitosa',
    info: 'Información',
    warning: 'Advertencia',
    loading: 'Cargando...',
    // Common errors
    failedToUpdate: 'Error al actualizar datos. Inténtalo de nuevo.',
    fillAllFields: 'Por favor, completa todos los campos',
    operationFailed: 'La operación falló. Inténtalo de nuevo.',
    // Error boundary
    unexpectedError: 'Error Inesperado',
    somethingWentWrong: 'Algo salió mal. Por favor, inténtalo más tarde.',
    somethingWentWrongPage: 'Algo salió mal al cargar esta página.',
    returnToHome: 'Volver al Inicio',
    // Success messages
    operationCompleted: 'Operación completada exitosamente',
    dataSaved: 'Datos guardados exitosamente',
    dataUpdated: 'Datos actualizados exitosamente',
    // Connection messages
    reconnecting: 'Reconectando al servidor...',
    reconnected: 'Conexión al servidor restaurada',
    connectionFailed: 'Error al restaurar la conexión al servidor',
    // Segmentation messages
    segmentationRequested: 'Solicitud de segmentación enviada',
    segmentationCompleted: 'Segmentación de imagen completada',
    segmentationFailed: 'La segmentación falló',
    segmentationResultFailed: 'Error al obtener el resultado de segmentación',
    segmentationStatusFailed: 'Error al verificar el estado de segmentación',
    // Export messages
    exportCompleted: '¡Exportación completada exitosamente!',
    exportFailed: 'La exportación falló. Inténtalo de nuevo.',
    // Project actions
    project: {
      created: 'Proyecto creado exitosamente',
      createFailed: 'Error al crear el proyecto',
      deleted: 'Proyecto eliminado exitosamente',
      deleteFailed: 'Error al eliminar el proyecto',
      urlCopied: 'URL del proyecto copiada al portapapeles',
      notFound: 'Proyecto no encontrado',
      invalidResponse: 'La respuesta del servidor fue inválida',
      readyForImages: 'está listo para imágenes',
    },
    // Profile actions
    profile: {
      consentUpdated:
        'Preferencias de consentimiento actualizadas exitosamente',
      loadFailed: 'Error al cargar datos del perfil',
    },
    // Upload actions
    upload: {
      failed: 'Error al actualizar imágenes después de la carga',
    },
    // Segmentation actions
    segmentation: {
      saved: 'Segmentación guardada exitosamente',
      failed: 'Error al guardar segmentación',
      deleted: 'Polígono eliminado',
    },
  },
  export: {
    // Dialog headers
    advancedOptions: 'Opciones Avanzadas de Exportación',
    configureSettings:
      'Configure los ajustes de exportación para crear un paquete de datos integral',
    // Tabs
    general: 'General',
    visualization: 'Visualización',
    formats: 'Formatos',
    // Content selection
    exportContents: 'Contenido de Exportación',
    selectContent:
      'Seleccione qué tipos de contenido incluir en su exportación',
    includeOriginal: 'Incluir imágenes originales',
    includeVisualizations: 'Incluir visualizaciones con polígonos numerados',
    includeDocumentation: 'Incluir documentación y metadatos',
    // Image selection
    selectedImages: 'Imágenes Seleccionadas',
    imagesSelected: '{{count}} de {{total}} imágenes seleccionadas',
    selectAll: 'Seleccionar Todo',
    selectNone: 'No Seleccionar Ninguna',
    imageSelection: 'Selección de Imágenes',
    chooseImages: 'Elija qué imágenes incluir en la exportación',
    // Quality settings
    qualitySettings: 'Configuración de Calidad',
    imageQuality: 'Calidad de Imagen',
    compressionLevel: 'Nivel de Compresión',
    outputResolution: 'Resolución de Salida',
    // Visualization settings
    colorSettings: 'Configuración de Color',
    backgroundColor: 'Color de Fondo',
    strokeColor: 'Color de Trazo',
    strokeWidth: 'Grosor de Trazo',
    fontSize: 'Tamaño de Fuente',
    showNumbers: 'Mostrar números de polígonos',
    showLabels: 'Mostrar etiquetas',
    // Format options
    outputSettings: 'Configuración de Salida',
    exportFormats: 'Formatos de Exportación',
    exportToZip: 'Exportar a archivo ZIP',
    generateExcel: 'Generar métricas de Excel',
    includeCocoFormat: 'Incluir anotaciones en formato COCO',
    includeJsonMetadata: 'Incluir metadatos JSON',
    // Progress and status
    preparing: 'Preparando exportación...',
    processing: 'Procesando {{current}} de {{total}}',
    packaging: 'Creando paquete...',
    completed: 'Exportación completada',
    downloading: 'Descargando...',
    cancelled: 'Exportación cancelada',
    // Connection status
    connected: 'Conectado',
    disconnected: 'Desconectado',
    reconnecting: 'Reconectando...',
    // Buttons
    startExport: 'Iniciar Exportación',
    cancel: 'Cancelar',
    download: 'Descargar',
    retry: 'Reintentar',
    close: 'Cerrar',
    // Error messages
    exportError: 'La exportación falló',
    exportFailed: 'Exportación fallida',
    exportComplete: 'Exportación completada',
    metricsExportComplete: 'Exportación de métricas completada',
    connectionError: 'Conexión perdida durante la exportación',
    serverError: 'Error del servidor ocurrido',
    invalidSelection: 'Por favor seleccione al menos una imagen',
    noData: 'No hay datos disponibles para exportar',
  },
  // Standalone image action messages (used without prefix)
  imageDeleted: 'Imagen eliminada exitosamente',
  deleteImageFailed: 'Error al eliminar imagen',
  deleteImageError: 'Error al eliminar imagen',
  imageAlreadyProcessing: 'La imagen ya está siendo procesada',
  processImageFailed: 'Error al procesar la imagen',

  exportDialog: {
    title: 'Opciones de Exportación',
    includeMetadata: 'Incluir metadatos',
    includeSegmentation: 'Incluir segmentación',
    includeObjectMetrics: 'Incluir métricas de objetos',
    exportMetricsOnly: 'Exportar solo métricas (XLSX)',
    selectImages: 'Seleccionar imágenes para exportar',
    selectAll: 'Seleccionar Todo',
    selectNone: 'Deseleccionar Todo',
    noImagesAvailable: 'No hay imágenes disponibles',
  },
  docs: {
    // Header section
    badge: 'Documentación',
    title: 'Documentación de SpheroSeg',
    subtitle:
      'Guía completa para usar nuestra plataforma de segmentación de esferoides',

    // Navigation
    navigation: 'Navegación',

    // Navigation items
    nav: {
      introduction: 'Introducción',
      gettingStarted: 'Comenzando',
      uploadingImages: 'Subir Imágenes',
      modelSelection: 'Selección de Modelo',
      segmentationProcess: 'Proceso de Segmentación',
      segmentationEditor: 'Editor de Segmentación',
      exportFeatures: 'Características de Exportación',
    },

    // Introduction section
    introduction: {
      title: 'Introducción',
      whatIs: '¿Qué es SpheroSeg?',
      description:
        'SpheroSeg es una plataforma avanzada diseñada específicamente para la segmentación y análisis de esferoides celulares en imágenes microscópicas. Nuestra herramienta combina algoritmos de IA de vanguardia con una interfaz intuitiva para proporcionar a los investigadores capacidades precisas de detección y análisis de límites de esferoides.',
      developedBy:
        'Esta plataforma fue desarrollada por Bc. Michal Průšek, estudiante de la Facultad de Ciencias Nucleares e Ingeniería Física de la Universidad Técnica Checa en Praga, bajo la supervisión de Ing. Adam Novozámský, Ph.D. El proyecto es una colaboración con investigadores del Instituto de Bioquímica y Microbiología de UCT Praga.',
      addresses:
        'SpheroSeg aborda la tarea desafiante de identificar y segmentar con precisión los límites de esferoides en imágenes microscópicas, un paso crítico en muchos flujos de trabajo de investigación biomédica que involucran modelos de cultivo celular 3D.',
    },

    // Getting Started section
    gettingStarted: {
      title: 'Comenzando',
      accountCreation: 'Creación de Cuenta',
      accountDescription:
        'Para usar SpheroSeg, necesitarás crear una cuenta. Esto nos permite almacenar tus proyectos e imágenes de forma segura.',
      accountSteps: {
        step1: 'Navega a la página de registro',
        step2:
          'Ingresa tu dirección de correo electrónico institucional y crea una contraseña',
        step3: 'Completa tu perfil con tu nombre e institución',
        step4:
          'Verifica tu dirección de correo electrónico a través del enlace enviado a tu bandeja de entrada',
      },
      firstProject: 'Creando Tu Primer Proyecto',
      projectDescription:
        'Los proyectos te ayudan a organizar tu trabajo. Cada proyecto puede contener múltiples imágenes y sus resultados de segmentación correspondientes.',
      projectSteps: {
        step1: 'Desde tu panel de control, haz clic en "Nuevo Proyecto"',
        step2: 'Ingresa un nombre y descripción del proyecto',
        step3:
          'Selecciona el tipo de proyecto (predeterminado: Análisis de Esferoides)',
        step4: 'Haz clic en "Crear Proyecto" para continuar',
      },
    },

    // Upload Images section
    uploadImages: {
      title: 'Subir Imágenes',
      description:
        'SpheroSeg admite varios formatos de imagen comúnmente utilizados en microscopía, incluyendo TIFF, PNG y JPEG.',
      methods: 'Métodos de Subida',
      methodsDescription: 'Hay múltiples formas de subir tus imágenes:',
      methodsList: {
        dragDrop:
          'Arrastra y suelta archivos directamente en el área de subida',
        browse:
          'Haz clic en el área de subida para navegar y seleccionar archivos de tu computadora',
        batch: 'Subida por lotes de múltiples imágenes a la vez',
      },
      note: 'Nota:',
      noteText:
        'Para resultados óptimos, asegúrate de que tus imágenes microscópicas tengan buen contraste entre el esferoide y el fondo.',
    },

    // Model Selection section
    modelSelection: {
      title: 'Selección de Modelo',
      description:
        'SpheroSeg ofrece tres modelos de IA diferentes optimizados para diferentes casos de uso. Elige el modelo que mejor se ajuste a tus requisitos de velocidad versus precisión.',
      models: {
        hrnet: {
          name: 'HRNet (Pequeño)',
          inferenceTime: 'Tiempo de inferencia: ~3.1 segundos',
          bestFor:
            'Mejor para: Procesamiento en tiempo real y resultados rápidos',
          description:
            'Modelo rápido y eficiente ideal para segmentación rápida cuando la velocidad se prioriza sobre la máxima precisión.',
        },
        cbam: {
          name: 'CBAM-ResUNet (Mediano)',
          inferenceTime: 'Tiempo de inferencia: ~6.9 segundos',
          bestFor: 'Mejor para: Velocidad y precisión equilibradas',
          description:
            'Equilibrio óptimo entre velocidad de procesamiento y calidad de segmentación para la mayoría de casos de uso.',
        },
        ma: {
          name: 'MA-ResUNet (Grande)',
          inferenceTime: 'Tiempo de inferencia: ~18.1 segundos',
          bestFor: 'Mejor para: Máxima precisión',
          description:
            'Modelo de mayor precisión con mecanismos de atención para la detección más precisa de límites de esferoides.',
        },
      },
      howToSelect: 'Cómo Seleccionar un Modelo',
      selectionSteps: {
        step1: 'Abre tu proyecto y navega a cualquier imagen',
        step2:
          'En la barra de herramientas del proyecto, encuentra el menú desplegable de selección de modelo',
        step3: 'Elige entre HRNet, CBAM-ResUNet o MA-ResUNet',
        step4:
          'Ajusta el umbral de confianza (0.0-1.0) para afinar la sensibilidad de detección',
        step5:
          'Tu selección se guarda automáticamente para procesamiento futuro',
      },
      tip: 'Consejo:',
      tipText:
        'Comienza con CBAM-ResUNet para la mayoría de casos. Usa HRNet para prototipado rápido y MA-ResUNet cuando necesites la mayor precisión posible para investigación o publicación.',
    },

    // Segmentation Process section
    segmentationProcess: {
      title: 'Proceso de Segmentación',
      description:
        'El proceso de segmentación utiliza modelos de IA avanzados para detectar automáticamente los límites de esferoides en tus imágenes microscópicas. El sistema soporta tanto procesamiento automático como refinamiento manual.',
      queueBased: 'Procesamiento Basado en Cola',
      queueDescription:
        'SpheroSeg utiliza un sistema de cola de procesamiento para manejar múltiples tareas de segmentación de manera eficiente:',
      queueFeatures: {
        realTime:
          'Estado en tiempo real: Las notificaciones WebSocket proporcionan actualizaciones en vivo del progreso de procesamiento',
        batch:
          'Procesamiento por lotes: Procesa múltiples imágenes simultáneamente',
        priority:
          'Manejo de prioridad: Las solicitudes más recientes se procesan primero',
        recovery:
          'Recuperación de errores: Los trabajos fallidos se reintentan automáticamente con informes detallados de errores',
      },
      workflow: 'Flujo de Trabajo de Segmentación Automática',
      workflowSteps: {
        step1: 'Sube tus imágenes microscópicas a un proyecto',
        step2:
          'Selecciona tu modelo de IA preferido (HRNet, CBAM-ResUNet o MA-ResUNet)',
        step3:
          'Ajusta el umbral de confianza si es necesario (predeterminado: 0.5)',
        step4:
          'Haz clic en "Auto-Segmentar" o usa procesamiento por lotes para múltiples imágenes',
        step5:
          'Monitorea el progreso en tiempo real a través de los indicadores de estado',
        step6:
          'Revisa los resultados en el editor de segmentación una vez que se complete el procesamiento',
      },
      polygonTypes: 'Tipos de Polígonos',
      polygonDescription: 'El sistema detecta dos tipos de polígonos:',
      polygonTypesList: {
        external:
          'Polígonos externos: Límites principales de esferoides (mostrados en verde por defecto)',
        internal:
          'Polígonos internos: Agujeros o estructuras internas dentro de esferoides (mostrados en rojo por defecto)',
      },
      processingNote: 'Los tiempos de procesamiento varían según el modelo:',
      processingTimes:
        'HRNet (~3s), CBAM-ResUNet (~7s), MA-ResUNet (~18s). Elige según tus requisitos de precisión y restricciones de tiempo.',
    },

    // Segmentation Editor section
    segmentationEditor: {
      title: 'Editor de Segmentación',
      description:
        'El editor de segmentación es una herramienta poderosa para refinar segmentaciones generadas por IA y crear anotaciones manuales. Cuenta con múltiples modos de edición, atajos de teclado y herramientas avanzadas de manipulación de polígonos.',
      editingModes: 'Modos de Edición',
      modes: {
        view: {
          title: 'Modo Vista',
          description:
            'Navega e inspecciona polígonos sin hacer cambios. Haz clic en polígonos para seleccionarlos y ver detalles.',
        },
        editVertices: {
          title: 'Editar Vértices',
          description:
            'Arrastra vértices individuales para refinar límites de polígonos. Control preciso para ajustes de límites.',
        },
        addPoints: {
          title: 'Agregar Puntos',
          description:
            'Inserta nuevos vértices entre los existentes. Shift+clic para colocación automática de puntos.',
        },
        createPolygon: {
          title: 'Crear Polígono',
          description:
            'Dibuja nuevos polígonos desde cero. Haz clic para agregar puntos, doble clic para completar.',
        },
        sliceMode: {
          title: 'Modo Cortar',
          description:
            'Corta polígonos en múltiples partes dibujando líneas a través de ellos.',
        },
        deletePolygon: {
          title: 'Eliminar Polígono',
          description:
            'Elimina polígonos no deseados haciendo clic en ellos. Útil para eliminar detecciones falsas.',
        },
      },
      keyFeatures: 'Características Clave',
      features: {
        undoRedo:
          'Sistema Deshacer/Rehacer: Seguimiento completo del historial con soporte Ctrl+Z/Ctrl+Y',
        autoSave:
          'Guardado automático: Guardado periódico con indicadores visuales mostrando cambios no guardados',
        zoomPan:
          'Zoom y Panorámica: Zoom con rueda del ratón y navegación arrastrando',
        polygonManagement:
          'Gestión de Polígonos: Mostrar/ocultar, renombrar y operaciones por lotes',
        keyboardShortcuts:
          'Atajos de Teclado: Teclas rápidas completas para edición eficiente',
        realTimeFeedback:
          'Retroalimentación en Tiempo Real: Vista previa en vivo de ediciones y actualizaciones de estado',
      },
      shortcuts: 'Atajos de Teclado Esenciales',
      shortcutCategories: {
        navigation: 'Navegación:',
        actions: 'Acciones:',
      },
      shortcutsList: {
        v: 'Modo vista',
        e: 'Editar vértices',
        a: 'Agregar puntos',
        n: 'Crear polígono',
        ctrlZ: 'Deshacer',
        ctrlY: 'Rehacer',
        ctrlS: 'Guardar',
        delete: 'Eliminar seleccionado',
      },
      workingWithPolygons: 'Trabajando con Polígonos',
      polygonSteps: {
        step1:
          'Selecciona un polígono haciendo clic en él (resaltado en azul cuando está seleccionado)',
        step2: 'Cambia al modo de edición apropiado para tu tarea',
        step3: 'Haz tus modificaciones usando interacciones del ratón',
        step4:
          'Usa el panel de polígonos a la derecha para gestionar visibilidad y propiedades',
        step5:
          'Guarda tus cambios periódicamente o confía en el guardado automático',
      },
    },

    // Export Features section
    exportFeatures: {
      title: 'Características de Exportación',
      description:
        'SpheroSeg proporciona capacidades de exportación completas para integrar con tu flujo de trabajo de investigación. Exporta datos de segmentación en múltiples formatos adecuados para frameworks de aprendizaje automático y herramientas de análisis.',
      packageContents: 'Contenidos del Paquete de Exportación',
      contents: {
        originalImages: {
          title: 'Imágenes Originales',
          description:
            'Imágenes microscópicas originales de alta calidad en su formato nativo.',
        },
        visualizations: {
          title: 'Visualizaciones',
          description:
            'Imágenes anotadas con polígonos numerados y colores personalizables.',
        },
      },
      annotationFormats: 'Formatos de Anotación',
      formats: {
        coco: 'Formato COCO: Common Objects in Context - formato estándar para frameworks de detección de objetos como PyTorch y TensorFlow',
        yolo: 'Formato YOLO: You Only Look Once - formato optimizado para modelos de detección basados en YOLO',
        json: 'JSON Personalizado: Formato JSON estructurado con coordenadas detalladas de polígonos y metadatos',
      },
      calculatedMetrics: 'Métricas Calculadas',
      metricsDescription:
        'SpheroSeg calcula automáticamente métricas morfológicas completas para cada esferoide detectado:',
      metricsCategories: {
        basic: {
          title: 'Mediciones Básicas:',
          items: {
            area: 'Área (píxeles y unidades escaladas)',
            perimeter: 'Perímetro',
            diameter: 'Diámetro equivalente',
            circularity: 'Circularidad',
          },
        },
        advanced: {
          title: 'Métricas Avanzadas:',
          items: {
            feret: 'Diámetros de Feret (máx, mín, relación de aspecto)',
            majorMinor: 'Diámetro mayor/menor a través del centroide',
            compactness: 'Compacidad, convexidad, solidez',
            sphericity: 'Índice de esfericidad',
          },
        },
      },
      exportFormats: 'Formatos de Exportación de Métricas',
      exportFormatsList: {
        excel:
          'Excel (.xlsx): Hoja de cálculo formateada con hojas separadas para resumen y datos detallados',
        csv: 'CSV: Valores separados por comas para fácil importación en software estadístico',
        jsonExport:
          'JSON: Formato de datos estructurado para análisis programático',
      },
      visualizationCustomization: 'Personalización de Visualización',
      customizationOptions: {
        colors:
          'Colores de polígonos: Personaliza colores de polígonos externos (verde) e internos (rojo)',
        numbering:
          'Numeración: Mostrar/ocultar números de polígonos para identificación',
        strokeWidth: 'Ancho de trazo: Ajustar grosor de línea (1-10px)',
        fontSize:
          'Tamaño de fuente: Controlar tamaño de texto para números de polígonos (10-30px)',
        transparency:
          'Transparencia: Establecer transparencia de relleno de polígonos (0-100%)',
      },
      howToExport: 'Cómo Exportar',
      exportSteps: {
        step1: 'Navega al panel de tu proyecto',
        step2:
          'Selecciona las imágenes que quieres exportar (o exportar todas)',
        step3:
          'Haz clic en "Exportación Avanzada" para abrir el diálogo de exportación',
        step4:
          'Configura tus ajustes de exportación en las tres pestañas: General, Visualización y Formatos',
        step5: 'Revisa el resumen de exportación',
        step6:
          'Haz clic en "Iniciar Exportación" para generar y descargar tu paquete',
      },
      exportNote: 'Los paquetes de exportación son completos:',
      exportNoteText:
        'Cada exportación incluye documentación, metadatos y todos los tipos de contenido seleccionados organizados en una estructura de carpetas clara para uso fácil.',
    },

    // Footer navigation
    footer: {
      backToHome: 'Volver al Inicio',
      backToTop: 'Volver Arriba',
    },
  },
  legal: {
    terms: {
      title: 'Términos de Servicio',
      lastUpdated: 'Última actualización: enero 2025',
      disclaimer:
        'Al usar SpheroSeg, aceptas estos términos. Por favor, léelos cuidadosamente.',
      sections: {
        acceptance: {
          title: '1. Aceptación de Términos',
          content:
            'Al acceder o usar SpheroSeg ("el Servicio"), aceptas estar vinculado por estos Términos de Servicio ("Términos") y todas las leyes y reglamentos aplicables. Si no estás de acuerdo con alguno de estos términos, tienes prohibido usar este servicio. Estos Términos constituyen un acuerdo legalmente vinculante entre tú y SpheroSeg.',
        },
        useLicense: {
          title: '2. Licencia de Uso y Uso Permitido',
          content: 'Se otorga permiso para usar SpheroSeg para:',
          permittedUses: [
            'Propósitos de investigación personal y no comercial',
            'Investigación académica y educativa',
            'Publicaciones y estudios científicos',
            'Investigación y análisis biomédico',
          ],
          licenseNote:
            'Esta es la concesión de una licencia, no una transferencia de título. No puedes usar el servicio para propósitos comerciales sin consentimiento escrito explícito.',
        },
        dataUsage: {
          title: '3. Uso de Datos e Inteligencia Artificial',
          importantTitle: 'Importante: Uso de Tus Datos',
          importantContent:
            'Al subir imágenes y datos a SpheroSeg, consientes que usemos estos datos para mejorar y entrenar nuestros modelos de aprendizaje automático para una mejor precisión de segmentación.',
          ownershipTitle: 'Propiedad de datos:',
          ownershipContent:
            'Retienes la propiedad de todos los datos que subas a SpheroSeg. Sin embargo, al usar nuestro servicio, nos otorgas permiso para:',
          permissions: [
            'Procesar tus imágenes para análisis de segmentación',
            'Usar datos subidos (en forma anonimizada) para mejorar nuestros algoritmos de ML',
            'Mejorar la precisión del modelo a través del aprendizaje continuo',
            'Desarrollar nuevas características y capacidades de segmentación',
          ],
          protectionNote:
            'Todos los datos usados para entrenamiento de ML son anonimizados y despojados de información identificativa. No compartimos tus datos sin procesar con terceros sin consentimiento explícito.',
        },
        userResponsibilities: {
          title: '4. Responsabilidades del Usuario',
          content: 'Aceptas:',
          responsibilities: [
            'Usar el servicio solo para propósitos legales',
            'Respetar los derechos de propiedad intelectual',
            'No intentar hacer ingeniería inversa o comprometer el servicio',
            'Proporcionar información precisa al crear una cuenta',
            'Mantener la seguridad de tus credenciales de cuenta',
          ],
        },
        serviceAvailability: {
          title: '5. Disponibilidad del Servicio y Limitaciones',
          content:
            'Aunque nos esforzamos por mantener la disponibilidad continua del servicio, SpheroSeg se proporciona "tal como está" sin garantías de ningún tipo. No garantizamos acceso ininterrumpido, y el servicio puede estar sujeto a mantenimiento, actualizaciones o indisponibilidad temporal.',
        },
        limitationLiability: {
          title: '6. Limitación de Responsabilidad',
          content:
            'En ningún caso SpheroSeg, sus desarrolladores o afiliados serán responsables de daños indirectos, incidentales, especiales, consecuentes o punitivos, incluyendo pero no limitado a pérdida de datos, ganancias u oportunidades de negocio, que surjan del uso del servicio.',
        },
        privacy: {
          title: '7. Privacidad y Protección de Datos',
          content:
            'Tu privacidad es importante para nosotros. Por favor, revisa nuestra Política de Privacidad, que gobierna cómo recopilamos, usamos y protegemos tu información personal y datos de investigación.',
        },
        changes: {
          title: '8. Cambios en los Términos',
          content:
            'Nos reservamos el derecho de modificar estos Términos en cualquier momento. Los cambios serán efectivos inmediatamente después de la publicación. Tu uso continuado del servicio constituye aceptación de los Términos modificados.',
        },
        termination: {
          title: '9. Terminación',
          content:
            'Cualquier parte puede terminar este acuerdo en cualquier momento. Tras la terminación, tu derecho a acceder al servicio cesará inmediatamente, aunque estos Términos permanecerán en efecto con respecto al uso previo.',
        },
        governingLaw: {
          title: '10. Ley Aplicable',
          content:
            'Estos Términos se rigen e interpretan de acuerdo con las leyes aplicables. Cualquier disputa será resuelta a través de arbitraje vinculante o en tribunales de jurisdicción competente.',
        },
      },
      contact: {
        title: 'Información de Contacto:',
        content:
          'Si tienes preguntas sobre estos Términos, por favor contá<NAME_EMAIL>',
      },
      navigation: {
        backToHome: 'Volver al Inicio',
        privacyPolicy: 'Política de Privacidad',
      },
    },
    privacy: {
      title: 'Política de Privacidad',
      lastUpdated: 'Última actualización: enero 2025',
      disclaimer:
        'Tu privacidad es importante para nosotros. Esta política explica cómo recopilamos, usamos y protegemos tus datos.',
      sections: {
        introduction: {
          title: '1. Introducción',
          content:
            'Esta Política de Privacidad explica cómo SpheroSeg ("nosotros", "nos", "nuestro") recopila, usa, protege y comparte tu información cuando usas nuestra plataforma para segmentación y análisis de esferoides. Al usar nuestro servicio, consientes a las prácticas de datos descritas en esta política.',
        },
        informationCollected: {
          title: '2. Información que Recopilamos',
          content:
            'Recopilamos información que nos proporcionas directamente cuando creas una cuenta, subes imágenes, creas proyectos e interactúas con nuestros servicios.',
          personalInfo: {
            title: '2.1 Información Personal',
            items: [
              'Nombre y dirección de correo electrónico',
              'Afiliación institucional u organizacional',
              'Credenciales de cuenta y preferencias',
              'Información de contacto para solicitudes de soporte',
            ],
          },
          researchData: {
            title: '2.2 Datos de Investigación e Imágenes',
            ownershipTitle: 'Tus Datos de Investigación',
            ownershipContent:
              'Retienes la propiedad completa de todas las imágenes y datos de investigación que subas a SpheroSeg. Nunca reclamamos propiedad de tu contenido.',
            items: [
              'Imágenes que subes para análisis',
              'Metadatos de proyectos y configuraciones',
              'Resultados de segmentación y anotaciones',
              'Parámetros de análisis y configuraciones personalizadas',
            ],
          },
          usageInfo: {
            title: '2.3 Información de Uso',
            items: [
              'Datos de registro y marcas de tiempo de acceso',
              'Información del dispositivo y tipo de navegador',
              'Patrones de uso e interacciones con características',
              'Métricas de rendimiento y reportes de errores',
            ],
          },
        },
        mlTraining: {
          title: '3. Aprendizaje Automático y Mejora de Datos',
          importantTitle:
            'Importante: Uso de Tus Datos para Entrenamiento de IA',
          importantIntro:
            'Para mejorar continuamente nuestros algoritmos de segmentación, podemos usar imágenes subidas y datos para entrenar y mejorar nuestros modelos de aprendizaje automático.',
          controlTitle: 'Tienes control completo sobre tus datos:',
          controlContent:
            'Durante la creación de cuenta, puedes elegir si permitir que tus datos se usen para entrenamiento de ML. Puedes cambiar estas preferencias en cualquier momento.',
          manageTitle: 'Para gestionar tu consentimiento:',
          manageContent:
            'Ve a Configuración → pestaña Privacidad en tu panel. Allí puedes habilitar o deshabilitar el consentimiento de entrenamiento de ML y elegir propósitos específicos (mejora de algoritmos, desarrollo de características) para los cuales pueden usarse tus datos.',
          howWeUse: {
            title: 'Cómo Usamos Tus Datos para ML:',
            items: [
              'Entrenamiento de Modelo: Las imágenes se usan para entrenar algoritmos de segmentación para mejor precisión',
              'Mejora de Algoritmos: Tus correcciones de segmentación ayudan a mejorar la detección automática',
              'Desarrollo de Características: Los patrones de uso guían el desarrollo de nuevas herramientas de análisis',
              'Aseguramiento de Calidad: Los datos ayudan a validar y probar nuevas versiones de modelos',
            ],
          },
          protection: {
            title: 'Protección de Datos en Entrenamiento de ML:',
            items: [
              'Anonimización: Todos los datos son anonimizados antes del uso en entrenamiento de ML',
              'Eliminación de Metadatos: La información identificativa personal e institucional es eliminada',
              'Procesamiento Seguro: El entrenamiento ocurre en entornos seguros y aislados',
              'Sin Distribución de Datos Sin Procesar: Tus imágenes originales nunca se comparten con terceros',
            ],
          },
        },
        howWeUse: {
          title: '4. Cómo Usamos Tu Información',
          content: 'Usamos la información recopilada para:',
          purposes: [
            'Proporcionar y mantener servicios de segmentación',
            'Procesar tus imágenes y generar resultados de análisis',
            'Mejorar nuestros algoritmos y desarrollar nuevas características',
            'Comunicarnos contigo sobre tu cuenta y actualizaciones',
            'Proporcionar soporte técnico y resolución de problemas',
            'Cumplir con obligaciones legales y proteger nuestros derechos',
          ],
        },
        dataSecurity: {
          title: '5. Seguridad y Protección de Datos',
          content: 'Implementamos medidas de seguridad robustas incluyendo:',
          measures: [
            'Cifrado de datos en tránsito y en reposo',
            'Auditorías de seguridad regulares y evaluaciones de vulnerabilidades',
            'Controles de acceso y sistemas de autenticación',
            'Procedimientos seguros de respaldo y recuperación ante desastres',
            'Entrenamiento de seguridad para empleados y limitaciones de acceso',
          ],
        },
        dataSharing: {
          title: '6. Compartir Datos y Terceros',
          noSaleStatement:
            'No vendemos tu información personal o datos de investigación.',
          sharingContent:
            'Podemos compartir información solo en estas circunstancias limitadas:',
          circumstances: [
            'Con tu consentimiento explícito',
            'Para cumplir con obligaciones legales u órdenes judiciales',
            'Con proveedores de servicios confiables que ayudan a operar nuestra plataforma (bajo estrictos acuerdos de confidencialidad)',
            'Para proteger nuestros derechos, seguridad o propiedad',
            'En forma anonimizada y agregada para publicaciones de investigación (con tu consentimiento)',
          ],
        },
        privacyRights: {
          title: '7. Tus Derechos de Privacidad y Opciones',
          content: 'Tienes derecho a:',
          rights: [
            'Acceso: Solicitar copias de tus datos personales y contenido de investigación',
            'Rectificación: Actualizar o corregir información inexacta',
            'Eliminación: Solicitar la eliminación de tu cuenta y datos asociados',
            'Portabilidad: Exportar tus datos en un formato legible por máquina',
            'Exclusión: Solicitar exclusión del entrenamiento de ML. Nota: Esto puede limitar las siguientes características: precisión de segmentación automática, recomendaciones de modelo personalizadas, sugerencias de umbral adaptativo, optimizaciones de procesamiento por lotes y futuras mejoras impulsadas por IA. Contacta al soporte para impactos específicos en tu cuenta.',
            'Restricción: Limitar cómo procesamos tu información',
          ],
          contactNote:
            'Para ejercer estos derechos, contá<NAME_EMAIL>. Responderemos dentro de 30 días.',
        },
        dataRetention: {
          title: '8. Retención de Datos',
          content:
            'Distinguimos entre datos personales y datos de entrenamiento de ML:',
          categories: [
            'Datos Personales/de Cuenta: Todos los identificadores personales, información de perfil, configuraciones de cuenta e historial de transacciones serán eliminados permanentemente dentro de 90 días del cierre de cuenta.',
            'Datos de Investigación: Imágenes originales y datos de proyecto vinculados a tu cuenta serán eliminados dentro de 90 días del cierre de cuenta.',
            'Datos de Entrenamiento de ML: Los datos usados para entrenamiento de ML son primero anonimizados/pseudonimizados para eliminar todos los identificadores personales. Estos datos anonimizados pueden retenerse indefinidamente para preservar mejoras del modelo, a menos que específicamente te excluyas del entrenamiento de ML o solicites eliminación completa.',
            'Opciones de Exclusión: Puedes solicitar eliminación completa de todos los datos, incluyendo datos anonimizados de entrenamiento de ML, contactando <EMAIL>. El tiempo de procesamiento es típicamente 30 días.',
          ],
        },
        internationalTransfers: {
          title: '9. Transferencias Internacionales de Datos',
          content:
            'Tus datos pueden ser procesados en países distintos al tuyo. Aseguramos salvaguardas y protecciones apropiadas para transferencias internacionales, incluyendo cláusulas contractuales estándar y decisiones de adecuación.',
        },
        childrensPrivacy: {
          title: '10. Privacidad de Menores',
          content:
            'Nuestro servicio está destinado a investigadores y no está dirigido a menores de 16 años. No recopilamos conscientemente información personal de menores de 16 años. Si descubrimos tal recopilación, eliminaremos la información promptamente.',
        },
        policyChanges: {
          title: '11. Cambios a Esta Política',
          content:
            'Podemos actualizar esta Política de Privacidad para reflejar cambios en nuestras prácticas o requisitos legales. Te notificaremos de cambios materiales vía correo electrónico o aviso prominente en nuestro sitio web. El uso continuado constituye aceptación de términos actualizados.',
        },
        contact: {
          title: '12. Información de Contacto',
          dpo: 'Oficial de Protección de Datos: <EMAIL>',
          general: 'Consultas Generales: <EMAIL>',
          postal: 'Dirección Postal:',
          address: {
            line1: 'ÚTIA AV ČR',
            line2: 'Pod Vodárenskou věží 4',
            line3: '182 08 Praga 8',
            line4: 'República Checa',
          },
        },
      },
      navigation: {
        backToHome: 'Volver al Inicio',
        termsOfService: 'Términos de Servicio',
      },
    },
  },

  // WebSocket messages
  websocket: {
    reconnecting: 'Reconectando al servidor...',
    reconnected: 'Conexión al servidor restaurada',
    reconnectFailed: 'Error al restaurar la conexión al servidor',
    connectionLost: 'Conexión al servidor perdida',
    pollingMode: 'Usando modo de conexión de respaldo',
    upgradedToWebSocket: 'Actualizado a conexión en tiempo real',
    connectionError: 'No se puede conectar al servidor',
    authError: 'Error de autenticación',
  },
};
