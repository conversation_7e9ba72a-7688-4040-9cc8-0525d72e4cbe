import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { apiClient } from '@/lib/api';
import { logger } from '@/lib/logger';
import { useLanguage } from '@/contexts/useLanguage';
import { useAuth } from '@/contexts/useAuth';
import {
  Share,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  LogIn,
  UserPlus,
  Loader2,
} from 'lucide-react';

interface ShareValidationData {
  project: { id: string; title: string; description: string | null };
  sharedBy: { email: string };
  status: string;
  email: string | null;
  needsLogin: boolean;
}

function ShareAcceptPage() {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [shareData, setShareData] = useState<ShareValidationData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [accepted, setAccepted] = useState(false);
  const [autoAccepting, setAutoAccepting] = useState(false);

  const validateToken = useCallback(async () => {
    if (!token) {
      setError(t('sharing.invitationInvalid'));
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const data = await apiClient.validateShareToken(token);
      setShareData(data);
      setError(null);
    } catch (error: unknown) {
      logger.error('Failed to validate share token:', error);

      let errorMessage = t('sharing.invitationInvalid');
      if (error && typeof error === 'object') {
        if (
          'response' in error &&
          error.response &&
          typeof error.response === 'object'
        ) {
          const resp = error.response as any;
          if (resp.data?.message) {
            errorMessage = resp.data.message;
          }
        } else if ('message' in error && typeof error.message === 'string') {
          errorMessage = error.message;
        }
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token, t]);

  const handleAccept = useCallback(async () => {
    if (!token) return;

    try {
      setAccepting(true);
      const result = await apiClient.acceptShareInvitation(token);

      if (result.needsLogin) {
        toast({
          title: t('sharing.loginToAccept'),
          description: t('sharing.loginToAccept'),
          variant: 'default',
        });
        // Store the share token in localStorage to process after login
        localStorage.setItem('pendingShareToken', token);
        // Redirect to login and then to dashboard
        navigate(`/sign-in?returnTo=/dashboard`);
        return;
      }

      setAccepted(true);
      toast({
        title: t('common.success'),
        description: t('sharing.invitationAccepted'),
      });

      // Redirect to the dashboard after a short delay
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (error: any) {
      logger.error('Failed to accept share invitation:', error);

      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        t('sharing.invitationInvalid');
      toast({
        title: t('error'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setAccepting(false);
    }
  }, [token, t, navigate]);

  useEffect(() => {
    if (!token) {
      setError(t('sharing.invitationInvalid'));
      setLoading(false);
      return;
    }

    validateToken();
  }, [token, t, validateToken]);

  // Track if auto-accept has been triggered
  const autoAcceptedRef = useRef(false);

  // Auto-accept invitation if user is already logged in and matches email
  useEffect(() => {
    if (shareData && user && !accepted && !autoAcceptedRef.current) {
      if (!shareData.needsLogin && shareData.status === 'pending') {
        // For email invitations, check if the logged-in user matches
        if (shareData.email && user.email === shareData.email) {
          autoAcceptedRef.current = true;
          setAutoAccepting(true);
          handleAccept();
        }
        // For link invitations, auto-accept for any logged-in user
        else if (!shareData.email) {
          autoAcceptedRef.current = true;
          setAutoAccepting(true);
          handleAccept();
        }
      }
    }
  }, [shareData, user, accepted, handleAccept]);

  const handleLogin = () => {
    // Store the share token in localStorage to process after login
    if (token) {
      localStorage.setItem('pendingShareToken', token);
    }
    // After login, redirect directly to dashboard
    navigate(`/sign-in?returnTo=/dashboard`);
  };

  const handleSignUp = () => {
    // Store the share token in localStorage to process after registration
    if (token) {
      localStorage.setItem('pendingShareToken', token);
    }
    // After registration, user will be auto-logged in and redirected to dashboard
    navigate('/sign-up');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6">
            <div className="flex items-center justify-center space-x-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>{t('common.loading')}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center space-x-2">
              <AlertCircle className="h-6 w-6 text-red-500" />
              <span>{t('error')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">{error}</p>
            {/* Removed back to dashboard button from error state */}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (accepted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center space-x-2">
              <CheckCircle className="h-6 w-6 text-green-500" />
              <span>{t('common.success')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p>{t('sharing.invitationAccepted')}</p>
            <p className="text-sm text-muted-foreground">
              {t('common.redirectingToDashboard')}...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!shareData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="max-w-lg w-full">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center space-x-2">
            <Share className="h-6 w-6 text-blue-500" />
            <span>{t('sharing.acceptInvitation')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-2">
            <h2 className="text-xl font-semibold">{shareData.project.title}</h2>
            {shareData.project.description && (
              <p className="text-muted-foreground">
                {shareData.project.description}
              </p>
            )}
          </div>

          <div className="bg-muted rounded-lg p-4 space-y-2">
            <p className="text-sm">
              <span className="font-medium">{t('sharing.sharedBy')}</span>
              <br />
              {shareData.sharedBy.email}
            </p>
            {shareData.email && (
              <p className="text-sm">
                <span className="font-medium">
                  {t('sharing.invitedEmail')}:
                </span>
                <br />
                {shareData.email}
              </p>
            )}
          </div>

          <div className="space-y-3">
            {shareData.needsLogin || !user ? (
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground text-center">
                  {t('sharing.loginToAccept')}
                </p>
                <Button onClick={handleLogin} className="w-full">
                  <LogIn className="h-4 w-4 mr-2" />
                  {t('auth.signIn')}
                </Button>
                <Button
                  onClick={handleSignUp}
                  className="w-full"
                  variant="outline"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  {t('auth.signUp')}
                </Button>
              </div>
            ) : (
              <Button
                onClick={handleAccept}
                disabled={accepting}
                className="w-full"
              >
                {accepting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {t('sharing.accepting')}...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {t('sharing.acceptInvitation')}
                  </>
                )}
              </Button>
            )}

            {/* Removed back to dashboard button */}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ShareAcceptPage;
