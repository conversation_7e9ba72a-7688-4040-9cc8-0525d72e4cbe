# Staging Environment Configuration Example
# Copy this file to .env.staging and fill in the values

# Database Configuration (Required)
DB_PASSWORD=<STRONG_PASSWORD_HERE>

# JWT Secrets - Generate using: openssl rand -base64 64
STAGING_JWT_ACCESS_SECRET=<GENERATE_UNIQUE_SECRET_HERE>
STAGING_JWT_REFRESH_SECRET=<GENERATE_UNIQUE_SECRET_HERE>

# Grafana Admin Password (Required)
GRAFANA_ADMIN_PASSWORD=<STRONG_PASSWORD_HERE>

# SMTP Configuration (if using external email)
SMTP_HOST=mail.utia.cas.cz
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASS=<EMAIL_PASSWORD_HERE>

# Notes:
# - Never commit actual secrets to version control
# - Generate strong, unique secrets for each environment
# - This file must exist with valid values for deployment to work
# - Store this file securely with restricted access (chmod 600)