# Staging Environment Configuration
# Copy this file to .env.staging and update with your values

# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_staging_password_here
POSTGRES_DB=spheroseg_staging

# Redis
REDIS_PASSWORD=your_redis_password_here

# JWT Secrets
JWT_SECRET=your_staging_jwt_secret_here
JWT_REFRESH_SECRET=your_staging_refresh_secret_here

# API URLs
FRONTEND_URL=https://staging.spherosegapp.utia.cas.cz
BACKEND_URL=https://staging.spherosegapp.utia.cas.cz/api
ML_SERVICE_URL=http://ml-service:8000

# Storage
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=104857600

# Email (optional for staging)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
ADMIN_EMAIL=<EMAIL>

# Monitoring
GRAFANA_ADMIN_PASSWORD=your_grafana_password_here

# Node Environment
NODE_ENV=staging

# ML Service
ML_MODEL_PATH=/app/weights
ML_BATCH_SIZE=4
ML_DEVICE=cpu