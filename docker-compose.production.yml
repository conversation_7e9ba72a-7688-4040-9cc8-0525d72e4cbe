version: '3.8'

services:
  # PostgreSQL Database for Production
  postgres-production:
    image: postgres:15-alpine
    container_name: spheroseg-postgres-production
    environment:
      POSTGRES_USER: spheroseg
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: spheroseg_production
    volumes:
      - postgres_production_data:/var/lib/postgresql/data
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg -d spheroseg_production"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Queue Management
  redis-production:
    image: redis:7-alpine
    container_name: spheroseg-redis-production
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend (React + Vite) - Green Environment
  green-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: frontend
    container_name: spheroseg-frontend-production
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://spherosegapp.utia.cas.cz
      - VITE_API_BASE_URL=https://spherosegapp.utia.cas.cz/api
      - VITE_ML_SERVICE_URL=https://spherosegapp.utia.cas.cz/ml
    ports:
      - "5000:3000"
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API (Node.js + Express) - Green Environment
  green-backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: backend
    container_name: spheroseg-backend-production
    env_file:
      - .env.production
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://spheroseg:${DB_PASSWORD}@postgres-production:5432/spheroseg_production
      - JWT_ACCESS_SECRET=${PRODUCTION_JWT_ACCESS_SECRET}
      - JWT_REFRESH_SECRET=${PRODUCTION_JWT_REFRESH_SECRET}
      - FROM_EMAIL=<EMAIL>
      - WS_ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz
    volumes:
      - ./backend/uploads/production:/app/backend/uploads
      - ./backend/data/production:/app/backend/data
    ports:
      - "5001:3001"
    depends_on:
      postgres-production:
        condition: service_healthy
      redis-production:
        condition: service_healthy
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ML Service (Python + FastAPI) - Green Environment
  green-ml:
    build:
      context: .
      dockerfile: Dockerfile
      target: ml
    container_name: spheroseg-ml-production
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://spheroseg:${DB_PASSWORD}@postgres-production:5432/spheroseg_production
      - REDIS_URL=redis://redis-production:6379
    volumes:
      - ./backend/segmentation/weights:/app/weights:ro
      - ./backend/uploads/production:/app/uploads
    ports:
      - "5008:8000"
    depends_on:
      postgres-production:
        condition: service_healthy
      redis-production:
        condition: service_healthy
    networks:
      - production-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx-production:
    image: nginx:alpine
    container_name: spheroseg-nginx-production
    volumes:
      - ./docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - green-frontend
      - green-backend
      - green-ml
    networks:
      - production-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for Monitoring
  prometheus-production:
    image: prom/prometheus:latest
    container_name: spheroseg-prometheus-production
    volumes:
      - ./monitoring/production-prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_production_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9092:9090"
    networks:
      - production-network
    restart: unless-stopped

  # Grafana for Visualization
  grafana-production:
    image: grafana/grafana:latest
    container_name: spheroseg-grafana-production
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_INSTALL_PLUGINS=redis-datasource
    volumes:
      - grafana_production_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "5032:3000"
    depends_on:
      - prometheus-production
    networks:
      - production-network
    restart: unless-stopped

networks:
  production-network:
    driver: bridge
    name: spheroseg-production

volumes:
  postgres_production_data:
    name: spheroseg_postgres_production
  prometheus_production_data:
    name: spheroseg_prometheus_production
  grafana_production_data:
    name: spheroseg_grafana_production