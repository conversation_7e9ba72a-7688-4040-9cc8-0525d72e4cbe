# Environment
NODE_ENV=development

# Server
PORT=3001
HOST=localhost

# Database
DATABASE_URL="file:./dev.db"

# JWT
JWT_ACCESS_SECRET=your-super-secret-jwt-access-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-jwt-refresh-key-change-this-in-production
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Email Service
EMAIL_SERVICE=sendgrid
# For SendGrid:
SENDGRID_API_KEY=your-sendgrid-api-key
# For SMTP:
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Cell Segmentation Platform"

# File Storage
STORAGE_TYPE=local
# For local storage:
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
# For S3-compatible storage:
S3_ENDPOINT=
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_BUCKET=
S3_REGION=

# Redis (optional, for caching and queues)
REDIS_URL=redis://localhost:6379

# Segmentation Service
SEGMENTATION_SERVICE_URL=http://localhost:8000

# CORS
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100