generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String              @id @default(uuid())
  email             String              @unique
  password          String
  emailVerified     Boolean             @default(false)
  verificationToken String?
  resetToken        String?
  resetTokenExpiry  DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  profile           Profile?
  projects          Project[]
  segmentationQueue SegmentationQueue[]
  sessions          Session[]
  sharedProjects    ProjectShare[]      @relation("SharedWith")
  projectsSharedByMe ProjectShare[]     @relation("SharedBy")

  @@map("users")
}

model Profile {
  id                            String    @id @default(uuid())
  userId                        String    @unique
  username                      String?   @unique
  avatarUrl                     String?
  // Avatar storage model: Files stored in /app/uploads/avatars/
  // Support for JPEG, PNG, GIF formats with automatic Sharp processing
  avatarPath                    String?   // Storage path for avatar file
  avatarMimeType                String?   // MIME type of avatar
  avatarSize                    Int?      // Size of avatar in bytes
  bio                           String?
  organization                  String?
  location                      String?
  title                         String?
  publicProfile                 Boolean   @default(false)
  preferredModel                String    @default("hrnet")
  modelThreshold                Float     @default(0.5)
  preferredLang                 String    @default("cs")
  preferredTheme                String    @default("light")
  emailNotifications            Boolean   @default(true)
  consentToMLTraining           Boolean   @default(true)
  consentToAlgorithmImprovement Boolean   @default(true)
  consentToFeatureDevelopment   Boolean   @default(true)
  consentUpdatedAt              DateTime?
  createdAt                     DateTime  @default(now())
  updatedAt                     DateTime  @updatedAt
  user                          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profiles")
}

model Project {
  id                String              @id @default(uuid())
  title             String
  description       String?
  userId            String
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  images            Image[]
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  segmentationQueue SegmentationQueue[]
  shares            ProjectShare[]

  @@index([userId, updatedAt], map: "idx_project_user_updated")
  @@map("projects")
}

model Image {
  id                 String              @id @default(uuid())
  name               String
  originalPath       String
  thumbnailPath      String?
  projectId          String
  segmentationStatus String              @default("no_segmentation")
  fileSize           Int?
  width              Int?
  height             Int?
  mimeType           String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  project            Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  queueEntries       SegmentationQueue[]
  segmentation       Segmentation?

  @@index([projectId, segmentationStatus], map: "idx_image_project_status")
  @@map("images")
}

model Segmentation {
  id                     String                  @id @default(uuid())
  imageId                String                  @unique
  polygons               String
  model                  String
  threshold              Float
  confidence             Float?
  processingTime         Int?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  imageHeight            Int?
  imageWidth             Int?
  image                  Image                   @relation(fields: [imageId], references: [id], onDelete: Cascade)
  segmentationThumbnails SegmentationThumbnail[]

  @@map("segmentations")
}

model SegmentationThumbnail {
  id              String        @id @default(uuid())
  segmentationId  String
  levelOfDetail   String        // 'low', 'medium', 'high'
  simplifiedData  String        // JSON string of simplified polygons
  polygonCount    Int
  pointCount      Int
  compressionRatio Float        // Original points / simplified points
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  segmentation    Segmentation  @relation(fields: [segmentationId], references: [id], onDelete: Cascade)

  @@unique([segmentationId, levelOfDetail])
  @@index([segmentationId, levelOfDetail], map: "idx_thumbnail_segmentation_lod")
  @@map("segmentation_thumbnails")
}

model SegmentationQueue {
  id          String    @id @default(uuid())
  imageId     String
  projectId   String
  userId      String
  model       String    @default("hrnet")
  threshold   Float     @default(0.5)
  detectHoles Boolean   @default(true)
  priority    Int       @default(0)
  status      String    @default("queued")
  error       String?
  retryCount  Int       @default(0)
  batchId     String?
  createdAt   DateTime  @default(now())
  startedAt   DateTime?
  completedAt DateTime?
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  image       Image     @relation(fields: [imageId], references: [id], onDelete: Cascade)

  @@index([status, priority, createdAt], map: "idx_queue_status_priority")
  @@index([projectId, status], map: "idx_project_status")
  @@map("segmentation_queue")
}

model Session {
  id           String   @id @default(uuid())
  userId       String
  refreshToken String   @unique
  userAgent    String?
  ipAddress    String?
  isValid      Boolean  @default(true)
  rememberMe   Boolean  @default(false)
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isValid, expiresAt], map: "idx_session_user_valid")
  @@map("sessions")
}

model ProjectShare {
  id           String    @id @default(uuid())
  projectId    String
  sharedById   String
  sharedWithId String?   // null for link-based shares before acceptance
  email        String?   // for email invitations
  shareToken   String    @unique // for shareable links
  tokenExpiry  DateTime? // optional expiry for share tokens
  status       String    @default("pending") // pending, accepted, revoked
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  
  project      Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  sharedBy     User      @relation("SharedBy", fields: [sharedById], references: [id], onDelete: Cascade)
  sharedWith   User?     @relation("SharedWith", fields: [sharedWithId], references: [id], onDelete: Cascade)

  @@index([projectId, status], map: "idx_share_project_status")
  @@index([shareToken], map: "idx_share_token")
  @@index([email, status], map: "idx_share_email_status")
  @@map("project_shares")
}
