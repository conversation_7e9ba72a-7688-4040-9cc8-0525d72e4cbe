# Git
.git
.gitignore

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Dependency directories
node_modules/

# Docker
.dockerignore
Dockerfile*
docker-compose*

# Environment - we'll copy specific env files manually
.env
.env.*

# Build outputs
dist
build

# Database files - we'll use volumes for persistence
dev.db
data/

# Uploads - we'll use volumes for persistence
uploads/

# Temporary files
tmp
temp

# Documentation
*.md
docs/

# Test files
test/
tests/
**/*.test.*
**/*.spec.*

# Prisma
.env
migrations/