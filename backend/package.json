{"name": "cell-segmentation-backend", "version": "1.0.0", "description": "Backend API for cell segmentation platform", "type": "module", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:seed": "tsx src/db/seed.ts", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "list-endpoints": "tsx -e \"import { routeRegistry } from './src/api/routes'; console.log('Registered API Endpoints:'); routeRegistry.forEach(r => console.log('  ' + r.method.padEnd(6) + ' ' + r.path + ' - ' + (r.description || 'No description')));\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "test-api": "echo 'Testing API endpoints...' && curl -s http://localhost:3001/health | jq '.' && curl -s http://localhost:3001/api/endpoints | jq '.data.count' && curl -s http://localhost:3001/metrics | head -5", "docker:build": "docker build -f ../docker/backend.Dockerfile -t spheroseg-backend .", "docker:run": "docker run -p 3001:3001 --env-file .env spheroseg-backend", "docker:dev": "docker-compose -f ../docker-compose.yml up backend -d"}, "keywords": ["cell-segmentation", "api", "backend"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@prisma/client": "^5.22.0", "archiver": "^6.0.2", "axios": "^1.7.8", "bcryptjs": "^2.4.3", "bull": "^4.16.3", "canvas": "^2.11.2", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.21.1", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.1", "form-data": "^4.0.4", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "nodemailer": "^6.10.1", "prom-client": "^15.1.3", "redis": "^4.7.0", "sharp": "^0.33.5", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/form-data": "^2.2.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.9.3", "@types/nodemailer": "^6.4.18", "@types/socket.io": "^3.0.1", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "jest": "^29.7.0", "prisma": "^5.22.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript": "^5.6.3"}}