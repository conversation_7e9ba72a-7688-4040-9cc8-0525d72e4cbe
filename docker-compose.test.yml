# Docker Compose configuration for testing
version: '3.8'

services:
  # Test Database
  test-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_DB: spheroseg_test
    ports:
      - "5433:5432"
    volumes:
      - test_db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d spheroseg_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Redis
  test-redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend for testing
  backend-test:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=test
      - DATABASE_URL=*************************************************/spheroseg_test
      - REDIS_URL=redis://test-redis:6379
      - JWT_SECRET=test-jwt-secret-key-for-testing-only
      - JWT_REFRESH_SECRET=test-jwt-refresh-secret-key-for-testing-only
      - ML_SERVICE_URL=http://ml-service-test:8000
      - PORT=3001
      - LOG_LEVEL=info
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ML Service for testing
  ml-service-test:
    build:
      context: .
      dockerfile: docker/ml-service.Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=test
      - LOG_LEVEL=info
      - MODEL_PATH=/app/models
      - MAX_IMAGE_SIZE=10485760  # 10MB
      - INFERENCE_TIMEOUT=300
    volumes:
      - ./backend/segmentation/weights:/app/models:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend for E2E testing
  frontend-test:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
      target: development
    ports:
      - "3000:5173"
    environment:
      - NODE_ENV=test
      - VITE_API_URL=http://backend-test:3001/api
      - VITE_ML_SERVICE_URL=http://ml-service-test:8000/api/v1
    depends_on:
      - backend-test
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Test Prometheus (for monitoring tests)
  test-prometheus:
    image: prom/prometheus:latest
    ports:
      - "9091:9090"
    volumes:
      - ./docker/prometheus/prometheus.test.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  test_db_data:
    driver: local

networks:
  default:
    name: spheroseg-test-network