#!/bin/bash

# =============================================================================
# COMPREHENSIVE PRE-COMMIT HOOK FOR SPHEROSEG PROJECT
# =============================================================================
# This hook performs extensive validation before allowing commits
# It's designed to work both locally and in Docker environments
# =============================================================================

set -o pipefail

# -----------------------------------------------------------------------------
# CONFIGURATION
# -----------------------------------------------------------------------------

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Counters
ERRORS=0
WARNINGS=0
CHECKS_PASSED=0
CHECKS_TOTAL=0

# Configuration flags (can be overridden by environment variables)
STRICT_MODE=${STRICT_MODE:-false}
AUTO_FIX=${AUTO_FIX:-true}
DOCKER_CHECKS=${DOCKER_CHECKS:-true}
SKIP_TESTS=${SKIP_TESTS:-false}

# -----------------------------------------------------------------------------
# HELPER FUNCTIONS
# -----------------------------------------------------------------------------

print_header() {
    echo ""
    echo -e "${BOLD}${MAGENTA}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}${MAGENTA}🔐 SPHEROSEG PRE-COMMIT VALIDATION${NC}"
    echo -e "${BOLD}${MAGENTA}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

print_section() {
    local title="$1"
    echo ""
    echo -e "${BOLD}${CYAN}▶ $title${NC}"
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    ((CHECKS_TOTAL++))
}

print_status() {
    local status="$1"
    local message="$2"
    local detail="$3"
    
    case "$status" in
        "success")
            echo -e "  ${GREEN}✅${NC} $message"
            ((CHECKS_PASSED++))
            ;;
        "error")
            echo -e "  ${RED}❌${NC} $message"
            if [ -n "$detail" ]; then
                echo -e "     ${RED}→ $detail${NC}"
            fi
            ((ERRORS++))
            ;;
        "warning")
            echo -e "  ${YELLOW}⚠️${NC}  $message"
            if [ -n "$detail" ]; then
                echo -e "     ${YELLOW}→ $detail${NC}"
            fi
            ((WARNINGS++))
            ;;
        "info")
            echo -e "  ${BLUE}ℹ️${NC}  $message"
            ;;
        "skip")
            echo -e "  ${CYAN}⏭️${NC}  $message"
            ;;
        "working")
            echo -e "  ${CYAN}⏳${NC} $message"
            ;;
    esac
}

check_command() {
    local cmd="$1"
    if command -v "$cmd" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

get_changed_files() {
    git diff --cached --name-only --diff-filter=ACM
}

get_changed_files_by_type() {
    local extension="$1"
    get_changed_files | grep -E "\.${extension}$" || true
}

# -----------------------------------------------------------------------------
# VALIDATION CHECKS
# -----------------------------------------------------------------------------

# 1. GIT CHECKS
check_git_status() {
    print_section "GIT STATUS CHECK"
    
    # Check for merge conflicts
    if git diff --cached --name-only | xargs grep -l "<<<<<<\|>>>>>>\|======" 2>/dev/null | head -1 | grep -q .; then
        print_status "error" "Merge conflict markers found in staged files"
        return 1
    else
        print_status "success" "No merge conflict markers"
    fi
    
    # Check for large files
    local large_files=$(git diff --cached --name-only | xargs ls -la 2>/dev/null | awk '$5 > 5242880 {print $9}' | head -5)
    if [ -n "$large_files" ]; then
        print_status "warning" "Large files detected (>5MB):" "$large_files"
    else
        print_status "success" "No large files (>5MB)"
    fi
    
    # Check for sensitive files
    local sensitive_patterns=(".env" ".pem" ".key" "secret" "credentials" "password")
    local sensitive_found=false
    for pattern in "${sensitive_patterns[@]}"; do
        if get_changed_files | grep -i "$pattern" | grep -v ".example" | head -1 | grep -q .; then
            sensitive_found=true
            break
        fi
    done
    
    if [ "$sensitive_found" = true ]; then
        print_status "warning" "Potentially sensitive files detected - please review"
    else
        print_status "success" "No sensitive files detected"
    fi
}

# 2. CODE FORMATTING
check_formatting() {
    print_section "CODE FORMATTING"
    
    if check_command "npx" && [ -f "package.json" ]; then
        print_status "working" "Checking code formatting with Prettier..."
        
        if npx prettier --check . >/dev/null 2>&1; then
            print_status "success" "Code is properly formatted"
        else
            if [ "$AUTO_FIX" = true ]; then
                print_status "info" "Auto-formatting code..."
                npx prettier --write . >/dev/null 2>&1
                git add -u
                print_status "success" "Code has been auto-formatted"
            else
                print_status "error" "Code is not properly formatted" "Run: npm run format"
            fi
        fi
    else
        print_status "skip" "Prettier not available - skipping formatting check"
    fi
}

# 3. LINTING
check_linting() {
    print_section "CODE LINTING"
    
    # Frontend linting
    if check_command "npx" && [ -f "eslint.config.js" ]; then
        print_status "working" "Running ESLint..."
        
        # Check if ESLint dependencies are installed
        if [ -d "node_modules/eslint" ]; then
            local eslint_output
            eslint_output=$(npx eslint . --max-warnings 0 2>&1) || true
            
            if echo "$eslint_output" | grep -q "Cannot find package\|Error:"; then
                print_status "warning" "ESLint configuration issues - skipping"
            elif [ -z "$eslint_output" ] || echo "$eslint_output" | grep -q "0 problems"; then
                print_status "success" "ESLint passed with no errors or warnings"
            else
                if [ "$AUTO_FIX" = true ]; then
                    print_status "info" "Attempting to auto-fix ESLint issues..."
                    npx eslint . --fix >/dev/null 2>&1 || true
                    git add -u
                fi
                print_status "warning" "ESLint found issues" "Run: npm run lint:fix"
            fi
        else
            print_status "skip" "ESLint not properly installed - skipping"
        fi
    else
        print_status "skip" "ESLint not configured - skipping"
    fi
}

# 4. TYPE CHECKING
check_typescript() {
    print_section "TYPESCRIPT TYPE CHECKING"
    
    # Frontend TypeScript
    if [ -f "tsconfig.json" ] && check_command "npx"; then
        print_status "working" "Checking frontend TypeScript..."
        
        if npx tsc --noEmit 2>/dev/null; then
            print_status "success" "Frontend TypeScript check passed"
        else
            local ts_errors=$(npx tsc --noEmit 2>&1 | head -5)
            print_status "error" "Frontend TypeScript errors found" "$ts_errors"
        fi
    else
        print_status "skip" "Frontend TypeScript not configured"
    fi
    
    # Backend TypeScript
    if [ -f "backend/tsconfig.json" ] && check_command "npx"; then
        print_status "working" "Checking backend TypeScript..."
        
        if cd backend && npx tsc --noEmit 2>/dev/null; then
            print_status "success" "Backend TypeScript check passed"
            cd ..
        else
            local ts_errors=$(npx tsc --noEmit 2>&1 | head -5)
            print_status "error" "Backend TypeScript errors found" "$ts_errors"
            cd ..
        fi
    else
        print_status "skip" "Backend TypeScript not configured"
    fi
}

# 5. TESTING
check_tests() {
    print_section "UNIT TESTS"
    
    if [ "$SKIP_TESTS" = true ]; then
        print_status "skip" "Tests skipped (SKIP_TESTS=true)"
        return 0
    fi
    
    # Check if we're in Docker environment
    if [ -f /.dockerenv ] || [ -n "$DOCKER_CONTAINER" ]; then
        print_status "skip" "Running in Docker environment - skipping host tests"
        return 0
    fi
    
    # Try Docker tests first
    if [ "$DOCKER_CHECKS" = true ] && docker ps | grep -q "staging-backend"; then
        print_status "working" "Running tests in Docker containers..."
        
        if docker exec staging-backend npm run test -- --passWithNoTests 2>/dev/null; then
            print_status "success" "Backend tests passed"
        else
            print_status "warning" "Backend tests not available"
        fi
    else
        # Local tests fallback
        if check_command "npx" && [ -f "vitest.config.ts" ]; then
            print_status "working" "Running local tests..."
            
            if npx vitest run --passWithNoTests 2>/dev/null; then
                print_status "success" "Tests passed"
            else
                print_status "warning" "Some tests failed"
            fi
        else
            print_status "skip" "Test runner not configured"
        fi
    fi
}

# 6. SECURITY CHECKS
check_security() {
    print_section "SECURITY AUDIT"
    
    # npm audit
    if check_command "npm" && [ -f "package-lock.json" ]; then
        print_status "working" "Running security audit..."
        
        local audit_output=$(npm audit --audit-level=critical 2>&1)
        if echo "$audit_output" | grep -q "found 0 vulnerabilities"; then
            print_status "success" "No critical vulnerabilities found"
        elif echo "$audit_output" | grep -q "critical"; then
            print_status "error" "Critical vulnerabilities found" "Run: npm audit fix"
        else
            print_status "warning" "Some vulnerabilities found (not critical)"
        fi
    else
        print_status "skip" "npm audit not available"
    fi
    
    # Check for hardcoded secrets
    print_status "working" "Scanning for hardcoded secrets..."
    local secrets_found=false
    local secret_patterns=("api[_-]?key" "secret" "password" "token" "private[_-]?key")
    
    for pattern in "${secret_patterns[@]}"; do
        if get_changed_files | xargs grep -i "$pattern" 2>/dev/null | \
           grep -v "\.md\|\.test\.\|\.spec\.\|docker-compose\|\.env\.example\|\.env\.sample" | \
           head -1 | grep -q .; then
            secrets_found=true
            break
        fi
    done
    
    if [ "$secrets_found" = false ]; then
        print_status "success" "No hardcoded secrets detected"
    else
        print_status "warning" "Potential secrets found - please review"
    fi
}

# 7. CODE QUALITY
check_code_quality() {
    print_section "CODE QUALITY"
    
    # Check for console.log statements
    local console_files=$(get_changed_files_by_type "(js|jsx|ts|tsx)" | xargs grep -l "console\." 2>/dev/null | wc -l)
    if [ "$console_files" -eq 0 ]; then
        print_status "success" "No console statements in changed files"
    else
        print_status "warning" "Found console statements in $console_files file(s)"
    fi
    
    # Check for debugger statements
    if get_changed_files_by_type "(js|jsx|ts|tsx)" | xargs grep -l "debugger" 2>/dev/null | head -1 | grep -q .; then
        print_status "error" "Found debugger statements"
    else
        print_status "success" "No debugger statements"
    fi
    
    # Check for TODO/FIXME comments
    local todo_count=$(get_changed_files | xargs grep -c "TODO\|FIXME\|HACK\|XXX" 2>/dev/null | grep -v ":0" | wc -l)
    if [ "$todo_count" -eq 0 ]; then
        print_status "success" "No TODO/FIXME comments in changed files"
    else
        print_status "info" "Found TODO/FIXME in $todo_count file(s)"
    fi
}

# 8. DOCKER VALIDATION
check_docker() {
    print_section "DOCKER CONFIGURATION"
    
    if [ "$DOCKER_CHECKS" = false ]; then
        print_status "skip" "Docker checks disabled"
        return 0
    fi
    
    # Check if Docker files changed
    local docker_files=$(get_changed_files | grep -E "Dockerfile|docker-compose" || true)
    
    if [ -z "$docker_files" ]; then
        print_status "skip" "No Docker files changed"
        return 0
    fi
    
    print_status "working" "Validating Docker configurations..."
    
    # Validate docker-compose files
    for compose_file in docker-compose*.yml; do
        if [ -f "$compose_file" ]; then
            if docker compose -f "$compose_file" config >/dev/null 2>&1; then
                print_status "success" "$compose_file is valid"
            else
                print_status "error" "$compose_file has syntax errors"
            fi
        fi
    done
    
    # Check for exposed secrets in Docker files
    if get_changed_files | grep -E "Dockerfile|docker-compose" | \
       xargs grep -E "PASSWORD=|SECRET=|KEY=|TOKEN=" 2>/dev/null | \
       grep -v "ARG\|ENV.*=\${" | head -1 | grep -q .; then
        print_status "warning" "Potential secrets in Docker files - use environment variables"
    fi
}

# 9. DEPENDENCIES CHECK
check_dependencies() {
    print_section "DEPENDENCY VALIDATION"
    
    # Check package-lock.json consistency
    if [ -f "package-lock.json" ] && check_command "npm"; then
        print_status "working" "Checking package-lock.json consistency..."
        
        if npm ls >/dev/null 2>&1; then
            print_status "success" "package-lock.json is consistent"
        else
            print_status "warning" "package-lock.json may be out of sync" "Run: npm install"
        fi
    else
        print_status "skip" "package-lock.json not found"
    fi
    
    # Check for unused dependencies (if depcheck available)
    if check_command "npx" && npx depcheck --version >/dev/null 2>&1; then
        print_status "working" "Checking for unused dependencies..."
        local unused=$(npx depcheck --json 2>/dev/null | grep -c '"dependencies":\[\]' || echo "0")
        if [ "$unused" = "1" ]; then
            print_status "success" "No unused dependencies"
        else
            print_status "info" "Some unused dependencies detected"
        fi
    else
        print_status "skip" "depcheck not available"
    fi
}

# 10. COMMIT MESSAGE PREVIEW
check_commit_message() {
    print_section "COMMIT MESSAGE CHECK"
    
    # Check if commit message follows conventional commits
    if [ -f "commitlint.config.js" ]; then
        print_status "info" "Commit message will be validated by commitlint"
    else
        print_status "info" "Use conventional commits: feat:, fix:, docs:, style:, refactor:, test:, chore:"
    fi
}

# -----------------------------------------------------------------------------
# MAIN EXECUTION
# -----------------------------------------------------------------------------

main() {
    print_header
    
    # Get list of changed files
    local changed_files=$(get_changed_files)
    if [ -z "$changed_files" ]; then
        echo -e "${YELLOW}No files staged for commit${NC}"
        exit 0
    fi
    
    echo -e "${CYAN}Files staged for commit: $(echo "$changed_files" | wc -l) files${NC}"
    echo ""
    
    # Run all checks
    check_git_status
    check_formatting
    check_linting
    check_typescript
    check_tests
    check_security
    check_code_quality
    check_docker
    check_dependencies
    check_commit_message
    
    # Print summary
    echo ""
    echo -e "${BOLD}${MAGENTA}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}${CYAN}VALIDATION SUMMARY${NC}"
    echo -e "${BOLD}${MAGENTA}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    echo -e "  Checks passed: ${GREEN}$CHECKS_PASSED/$CHECKS_TOTAL${NC}"
    
    if [ $WARNINGS -gt 0 ]; then
        echo -e "  Warnings:      ${YELLOW}$WARNINGS${NC}"
    fi
    
    if [ $ERRORS -gt 0 ]; then
        echo -e "  Errors:        ${RED}$ERRORS${NC}"
    fi
    
    echo ""
    
    # Decide whether to allow commit
    if [ $ERRORS -gt 0 ]; then
        if [ "$STRICT_MODE" = true ]; then
            echo -e "${RED}${BOLD}✗ COMMIT BLOCKED${NC} - $ERRORS error(s) must be fixed"
            echo ""
            echo "To bypass (not recommended): STRICT_MODE=false git commit"
            exit 1
        else
            echo -e "${YELLOW}${BOLD}⚠ COMMIT ALLOWED WITH ERRORS${NC} - Please fix issues soon"
            echo ""
        fi
    elif [ $WARNINGS -gt 0 ]; then
        echo -e "${YELLOW}${BOLD}⚠ COMMIT ALLOWED WITH WARNINGS${NC}"
        echo ""
    else
        echo -e "${GREEN}${BOLD}✓ ALL CHECKS PASSED${NC} - Ready to commit!"
        echo ""
    fi
    
    exit 0
}

# Run main function
main "$@"