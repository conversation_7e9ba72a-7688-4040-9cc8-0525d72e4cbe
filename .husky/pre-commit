#!/bin/sh

# Simple pre-commit hook for SpheroSeg project
set -e

echo "🔐 Running pre-commit checks..."

# Check for merge conflict markers (actual Git conflict markers)
if git diff --cached --name-only | xargs grep -l "^<<<<<<< \|^>>>>>>> \|^=======$" 2>/dev/null | head -1 | grep -q .; then
    echo "❌ Error: Merge conflict markers found in staged files"
    exit 1
fi

# Check for debugger statements
if git diff --cached --name-only | grep -E "\.(js|jsx|ts|tsx)$" | xargs grep -l "debugger" 2>/dev/null | head -1 | grep -q .; then
    echo "❌ Error: Found debugger statements"
    exit 1
fi

# Run linting if available
if command -v npx >/dev/null 2>&1 && [ -f "package.json" ]; then
    echo "⏳ Running linter..."
    npx eslint . --max-warnings 0 || {
        echo "⚠️ Warning: ESLint found issues"
        echo "🔧 Attempting auto-fix..."
        npx eslint . --fix || true
        git add -u
    }
    
    echo "⏳ Checking code formatting..."
    npx prettier --check . || {
        echo "🔧 Auto-formatting code..."
        npx prettier --write .
        git add -u
    }
fi

# Check TypeScript if available
if [ -f "tsconfig.json" ] && command -v npx >/dev/null 2>&1; then
    echo "⏳ Checking TypeScript..."
    npx tsc --noEmit || {
        echo "❌ Error: TypeScript compilation failed"
        exit 1
    }
fi

echo "✅ Pre-commit checks completed successfully!"
exit 0