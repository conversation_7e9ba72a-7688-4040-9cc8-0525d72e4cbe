{"extends": "./tsconfig.app.json", "compilerOptions": {"jsx": "react-jsx", "allowImportingTsExtensions": false, "noEmit": true, "composite": true, "types": ["vitest/globals", "@testing-library/jest-dom", "node", "vite/client"], "lib": ["ES2020", "DOM", "DOM.Iterable"]}, "include": ["src/**/*", "tests/**/*", "vitest.setup.ts", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "exclude": ["node_modules", "dist", "backend"]}