# Environment Configuration Template
# ==================================
# This file shows all available environment variables and their purpose.
# Copy to .env.development or .env.production and customize values.

# QUICK START:
# 1. Copy to .env.development: cp .env.example .env.development
# 2. Generate JWT secrets: openssl rand -hex 32 (run twice)
# 3. Update JWT_ACCESS_SECRET and JWT_REFRESH_SECRET
# 4. Start development: make dev

# Application Environment
NODE_ENV=development  # Options: development, production, test

# Frontend URLs (Replace with your domain in production)
VITE_API_URL=http://localhost:3001/api
VITE_ML_SERVICE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:3001

# Backend Server Configuration
PORT=3001                    # Backend server port
HOST=0.0.0.0                # Server bind address

# Database Configuration
DATABASE_URL=file:./data/dev.db  # SQLite for dev, PostgreSQL for production

# JWT Security (CRITICAL: Generate cryptographically secure secrets!)
# REQUIRED: Generate a unique 32-byte (64 hex characters) secret for each key
# 
# To generate secure secrets, run:
#   openssl rand -hex 32
# Or in Node.js:
#   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
#
# SECURITY REQUIREMENTS:
# - Each secret MUST be exactly 64 hexadecimal characters (32 bytes)
# - NEVER commit real secrets to source control
# - Store production secrets in a secure secrets manager (e.g., AWS Secrets Manager, HashiCorp Vault)
# - Rotate secrets regularly (recommended: every 90 days)
# - Use different secrets for each environment (dev, staging, prod)
#
# Example format (DO NOT USE THESE - generate your own!):
# JWT_ACCESS_SECRET=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
# JWT_REFRESH_SECRET=fedcba9876543210fedcba9876543210fedcba9876543210fedcba9876543210
#
# Generate secrets using: openssl rand -hex 32
# PLACEHOLDER VALUES - Application will fail to start without valid secrets
JWT_ACCESS_SECRET=REPLACE_ME_GENERATE_32_BYTE_HEX
JWT_REFRESH_SECRET=REPLACE_ME_GENERATE_32_BYTE_HEX

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Docker Service Communication
SEGMENTATION_SERVICE_URL=http://ml-service:8000  # Always use service name

# File Storage
UPLOAD_DIR=/app/uploads      # Upload directory
EXPORT_DIR=/app/exports      # Export directory for segmentation results
STORAGE_TYPE=local           # Options: local, s3

# Email Service (Development)
EMAIL_SERVICE=sendgrid
SENDGRID_API_KEY=dummy-key-for-development
FROM_EMAIL=<EMAIL>

# Rate Limiting
RATE_LIMIT_ENABLED=false     # Enable in production
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=5000

# Monitoring
LOG_LEVEL=debug              # Options: debug, info, warn, error
PROMETHEUS_ENABLED=true
METRICS_PORT=9464

# Grafana Security Configuration
# IMPORTANT: Set strong, unique credentials for production environments
# - Use a password manager to generate a strong password (min 16 chars, mixed case, numbers, symbols)
# - Store credentials in a secure secrets manager
# - Enable 2FA in production Grafana instances
GF_SECURITY_ADMIN_USER=spheroseg_admin
# WARNING: NEVER use this placeholder password in production!
# REQUIRED: Generate a strong password before deployment (min 16 chars)
GF_SECURITY_ADMIN_PASSWORD=ChangeMe!Aa1Bb2Cc3Dd4Ee
GF_USERS_ALLOW_SIGN_UP=false

# ML Service Configuration
ML_MODEL_CACHE_SIZE=2        # Number of models to cache
ML_INFERENCE_TIMEOUT=300000  # 5 minutes
ML_BATCH_SIZE=2

# Development Control
PRISMA_DB_PUSH=false         # Auto-push Prisma schema changes
FASTAPI_RELOAD=true          # Enable FastAPI hot reload

# Production-only variables (not needed for development):
# AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_BUCKET_NAME
# REDIS_URL, REDIS_PASSWORD
# SSL_ENABLED, SSL_CERT_PATH, SSL_KEY_PATH
# DATABASE_CONNECTION_LIMIT, BACKUP_ENABLED