# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api
VITE_ML_SERVICE_URL=http://localhost:8000

# Development Configuration
NODE_ENV=development

# JWT Configuration (REQUIRED FOR PRODUCTION)
# Generate strong secrets (32+ bytes / 256+ bits) using secure random generators:
# Example: openssl rand -hex 32
# Example: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
# Store securely and NEVER commit real secrets to source control!
JWT_ACCESS_SECRET=REPLACE_WITH_SECURE_32_BYTE_SECRET_FROM_OPENSSL_RAND_HEX_32
JWT_REFRESH_SECRET=REPLACE_WITH_SECURE_32_BYTE_SECRET_FROM_OPENSSL_RAND_HEX_32

# Optional: Control Docker container behaviors
PRISMA_DB_PUSH=false
FASTAPI_RELOAD=false