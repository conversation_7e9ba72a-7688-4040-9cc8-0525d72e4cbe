version: '3.8'

services:
  nginx-prod:
    image: nginx:alpine
    container_name: nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.blue.conf:/etc/nginx/conf.d/default.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - ./docker/nginx/ssl-params.conf:/etc/nginx/ssl-params.conf:ro
    restart: always
    networks:
      - cell-segmentation-hub_blue-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  cell-segmentation-hub_blue-network:
    external: true