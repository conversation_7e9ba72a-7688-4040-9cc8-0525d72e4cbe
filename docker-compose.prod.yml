services:
  # Frontend Builder
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.prod.Dockerfile
      args:
        - VITE_API_BASE_URL=https://spherosegapp.utia.cas.cz/api
        - VITE_ML_SERVICE_URL=https://spherosegapp.utia.cas.cz/api/ml
        - VITE_WS_URL=wss://spherosegapp.utia.cas.cz
    container_name: spheroseg-frontend
    security_opt:
      - no-new-privileges:true
    read_only: false
    volumes:
      - frontend-static:/app/dist
    networks:
      - spheroseg-network
    restart: always

  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: spheroseg-nginx
    security_opt:
      - no-new-privileges:true
    read_only: false
    ports:
      - "80:80"
      - "443:443"
    networks:
      - spheroseg-network
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/sites:/etc/nginx/sites-enabled:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - frontend-static:/usr/share/nginx/html:ro
      - nginx-cache:/var/cache/nginx
      - ./docker/nginx/certbot:/var/www/certbot
    depends_on:
      - backend
      - frontend
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: backend.prod.Dockerfile
    container_name: spheroseg-backend
    security_opt:
      - no-new-privileges:true
    read_only: false
    networks:
      - spheroseg-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file: .env.production
    volumes:
      - ./backend/uploads:/app/uploads
      - backend-logs:/app/logs
    environment:
      - NODE_ENV=production
      - PORT=3001
      - HOST=0.0.0.0
      - DATABASE_URL=postgresql://spheroseg:${DB_PASSWORD}@spheroseg-db:5432/spheroseg_prod
      - REDIS_URL=redis://spheroseg-redis:6379
      - ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz
      - WS_ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz
      - SEGMENTATION_SERVICE_URL=http://spheroseg-ml:8000
      - UPLOAD_DIR=/app/uploads
      - STORAGE_TYPE=local
      - JWT_ACCESS_SECRET=${JWT_ACCESS_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX=1000
      - LOG_LEVEL=info
      - ENABLE_METRICS=true
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ML Service
  ml-service:
    build:
      context: ./backend/segmentation
      dockerfile: ../../docker/ml.prod.Dockerfile
    container_name: spheroseg-ml
    user: "1000:1000"
    security_opt:
      - no-new-privileges:true
    read_only: false
    networks:
      - spheroseg-network
    volumes:
      - ./backend/segmentation/weights:/app/weights:ro
      - ml-cache:/app/cache
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - WORKERS=2
      - MAX_REQUESTS=100
      - MAX_REQUESTS_JITTER=10
      - TIMEOUT=300
      - GRACEFUL_TIMEOUT=30
      - KEEP_ALIVE=5
      - LOG_LEVEL=info
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: spheroseg-db
    hostname: postgres
    user: "postgres:postgres"
    security_opt:
      - no-new-privileges:true
    read_only: false
    networks:
      - spheroseg-network
    environment:
      - POSTGRES_USER=spheroseg
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=spheroseg_prod
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - POSTGRES_HOST_AUTH_METHOD=md5
      - PGPORT=5432
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/db-backup:/backup
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg -d spheroseg_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis
    user: "redis:redis"
    security_opt:
      - no-new-privileges:true
    read_only: false
    networks:
      - spheroseg-network
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --port 6379
    volumes:
      - redis-data:/data
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: spheroseg-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - spheroseg-network
    restart: always

  # Grafana Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: spheroseg-grafana
    ports:
      - "3030:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=https://spherosegapp.utia.cas.cz/grafana
      - GF_SERVER_SERVE_FROM_SUB_PATH=true
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana-dashboards:/etc/grafana/provisioning/dashboards:ro
    networks:
      - spheroseg-network
    depends_on:
      - prometheus
    restart: always

  # Certbot for Let's Encrypt SSL automation
  certbot:
    image: certbot/certbot:latest
    container_name: spheroseg-certbot
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt
      - /var/lib/letsencrypt:/var/lib/letsencrypt
      - ./docker/nginx/certbot:/var/www/certbot
      - ./scripts/certbot-logs:/var/log/letsencrypt
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do sleep 12h & wait $${!}; certbot renew --webroot --webroot-path=/var/www/certbot; done;'"
    networks:
      - spheroseg-network
    depends_on:
      - nginx
    restart: unless-stopped

networks:
  spheroseg-network:
    driver: bridge

volumes:
  frontend-static:
    driver: local
  postgres-data:
    driver: local
  redis-data:
    driver: local
  backend-logs:
    driver: local
  ml-cache:
    driver: local
  nginx-cache:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  certbot-logs:
    driver: local