# Production Environment Configuration Template
# =============================================
# IMPORTANT: This file contains placeholder values for production secrets.
# Copy this file to .env.production and replace ALL placeholder values with actual production values before deployment.
# NEVER commit real production secrets to version control!

# Application Environment
NODE_ENV=production

# API Configuration
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_ML_SERVICE_URL=https://ml.yourdomain.com

# JWT Configuration (CRITICAL: Replace with secure secrets)
# Generate using: openssl rand -hex 32
# Load from secret manager or environment injection in production
JWT_ACCESS_SECRET=REPLACE_ME_WITH_SECURE_32_CHAR_HEX
JWT_REFRESH_SECRET=REPLACE_ME_WITH_SECURE_32_CHAR_HEX

# Database Configuration (PostgreSQL for production)
DATABASE_URL=*************************************************/spheroseg_production

# CORS Configuration (Production domains only)
ALLOWED_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# Email Service Configuration
EMAIL_SERVICE=sendgrid
SENDGRID_API_KEY=REPLACE_WITH_ACTUAL_SENDGRID_API_KEY
FROM_EMAIL=<EMAIL>

# Security Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# Storage Configuration
STORAGE_TYPE=s3
UPLOAD_DIR=/app/uploads
AWS_ACCESS_KEY_ID=REPLACE_WITH_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=REPLACE_WITH_AWS_SECRET_KEY
AWS_BUCKET_NAME=spheroseg-production-uploads
AWS_REGION=us-east-1

# Service URLs (Internal Docker network)
SEGMENTATION_SERVICE_URL=http://ml-service:8000

# Monitoring & Observability
LOG_LEVEL=info
PROMETHEUS_ENABLED=true
METRICS_PORT=9464

# Grafana Configuration
GF_SECURITY_ADMIN_USER=admin
GF_SECURITY_ADMIN_PASSWORD=REPLACE_WITH_SECURE_GRAFANA_PASSWORD
GF_USERS_ALLOW_SIGN_UP=false

# SSL/TLS Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/yourdomain.com.crt
SSL_KEY_PATH=/etc/ssl/private/yourdomain.com.key

# Database Connection Pool
DATABASE_CONNECTION_LIMIT=20
DATABASE_CONNECTION_TIMEOUT=30000

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=REPLACE_WITH_REDIS_PASSWORD

# ML Service Configuration
ML_MODEL_CACHE_SIZE=3
ML_INFERENCE_TIMEOUT=120000
ML_BATCH_SIZE=4

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30

# Feature Flags
FEATURE_REGISTRATION_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_EXPERIMENTAL_UI=false

# Security Headers
HSTS_MAX_AGE=31536000
CSP_ENABLED=true