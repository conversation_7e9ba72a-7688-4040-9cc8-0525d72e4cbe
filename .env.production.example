# Production Environment Configuration Example
# Copy this file to .env.production and fill in the values

# Database Configuration
DB_PASSWORD=<STRONG_PASSWORD_HERE>

# JWT Secrets - Generate using: openssl rand -base64 64
PRODUCTION_JWT_ACCESS_SECRET=<GENERATE_UNIQUE_SECRET_HERE>
PRODUCTION_JWT_REFRESH_SECRET=<GENERATE_UNIQUE_SECRET_HERE>

# Grafana Admin Password  
GRAFANA_ADMIN_PASSWORD=<STRONG_PASSWORD_HERE>

# SMTP Configuration (if using external email)
SMTP_HOST=mail.utia.cas.cz
SMTP_PORT=465
SMTP_USER=<EMAIL>
SMTP_PASS=<EMAIL_PASSWORD_HERE>

# Notes:
# - Never commit actual secrets to version control
# - Use a secrets management service in production (e.g., AWS Secrets Manager, HashiCorp Vault)
# - Generate strong, unique secrets for each environment
# - Rotate secrets regularly
# - Store this file securely with restricted access