import { z } from 'zod';
export declare const loginSchema: any;
export declare const registerSchema: any;
export declare const resetPasswordSchema: any;
export declare const confirmResetPasswordSchema: any;
export declare const createProjectSchema: any;
export declare const updateProjectSchema: any;
export declare const updateProfileSchema: any;
export declare const uploadImageSchema: any;
export declare const segmentationRequestSchema: any;
export declare const exportRequestSchema: any;
export declare const paginationSchema: any;
export declare const fileUploadSchema: any;
export declare const pointSchema: any;
export declare const polygonDataSchema: any;
export declare const segmentationDataSchema: any;
export declare const uuidSchema: any;
export declare const sortSchema: any;
export declare const searchSchema: any;
export declare const listProjectsSchema: any;
export declare const listImagesSchema: any;
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type CreateProjectFormData = z.infer<typeof createProjectSchema>;
export type UpdateProjectFormData = z.infer<typeof updateProjectSchema>;
export type UpdateProfileFormData = z.infer<typeof updateProfileSchema>;
export type SegmentationRequestData = z.infer<typeof segmentationRequestSchema>;
export type ExportRequestData = z.infer<typeof exportRequestSchema>;
//# sourceMappingURL=index.d.ts.map