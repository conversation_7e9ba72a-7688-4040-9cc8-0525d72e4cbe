services:
  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: spheroseg-db
    environment:
      POSTGRES_USER: spheroseg
      POSTGRES_PASSWORD: spheroseg_dev
      POSTGRES_DB: spheroseg
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg -d spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network
    restart: unless-stopped

  # Redis cache service
  # Used for:
  # - Optional caching to improve performance (gracefully degrades if unavailable)
  # - Future: Export queue management for large batch operations
  # - Future: WebSocket session management across multiple backend instances
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis
    networks:
      - spheroseg-network
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend service
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: spheroseg-frontend
    environment:
      - VITE_API_URL=http://localhost:3001
      - VITE_API_BASE_URL=http://localhost:3001/api
      - VITE_ML_SERVICE_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:3001
      - NODE_ENV=development
    ports:
      - "3000:5173"
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - ./index.html:/app/index.html
      - ./vite.config.ts:/app/vite.config.ts
      - ./tailwind.config.ts:/app/tailwind.config.ts
      - ./postcss.config.js:/app/postcss.config.js
      - ./tsconfig.json:/app/tsconfig.json
      - ./tsconfig.app.json:/app/tsconfig.app.json
    depends_on:
      - backend
    networks:
      - spheroseg-network
    restart: unless-stopped

  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: ../docker/backend.Dockerfile
    container_name: spheroseg-backend
    env_file: ${ENV_FILE:-.env.development}
    environment:
      - NODE_ENV=development
      - HOST=0.0.0.0
      - PORT=3001
      - DATABASE_URL=**************************************************/spheroseg
      - REDIS_URL=redis://redis:6379
      - JWT_ACCESS_SECRET=${JWT_ACCESS_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - SEGMENTATION_SERVICE_URL=http://ml-service:8000
      - ALLOWED_ORIGINS=http://localhost:3000
      - WS_ALLOWED_ORIGINS=http://localhost:3000
      - EMAIL_SERVICE=smtp
      - FROM_EMAIL=${FROM_EMAIL}
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
    ports:
      - "3001:3001"
    volumes:
      - ./backend/src:/app/src
      - ./backend/prisma:/app/prisma
      - ./backend/uploads:/app/uploads
      - backend-db:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      ml-service:
        condition: service_started
    networks:
      - spheroseg-network
    restart: unless-stopped

  # ML Service
  ml-service:
    build:
      context: ./backend/segmentation
      dockerfile: ../../docker/ml.Dockerfile
    container_name: spheroseg-ml
    # Modern GPU configuration for Docker Compose
    device_requests:
      - driver: nvidia
        count: 1
        capabilities: [gpu]
    env_file: ${ENV_FILE:-.env.development}
    ports:
      - "8000:8000"
    volumes:
      - ./backend/segmentation:/app
      - ./backend/segmentation/weights:/app/weights
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - DISPLAY=:99
      - QT_QPA_PLATFORM=offscreen
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
    networks:
      - spheroseg-network
    # Resource limits for memory
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 2G
    restart: unless-stopped

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: spheroseg-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - spheroseg-network
    restart: unless-stopped

  # Grafana visualization
  grafana:
    image: grafana/grafana:latest
    container_name: spheroseg-grafana
    env_file: ${ENV_FILE:-.env.development}
    ports:
      - "3030:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
    networks:
      - spheroseg-network
    depends_on:
      - prometheus
    restart: unless-stopped

  # MailHog email testing service
  mailhog:
    image: mailhog/mailhog
    container_name: spheroseg-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - spheroseg-network
    restart: unless-stopped

networks:
  spheroseg-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  ml-models:
    driver: local
  backend-db:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local