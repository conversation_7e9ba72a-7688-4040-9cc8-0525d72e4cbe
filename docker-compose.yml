services:
  # Database service
  postgres:
    image: postgres:15-alpine
    container_name: spheroseg-db
    environment:
      - POSTGRES_DB=spheroseg
      - POSTGRES_USER=spheroseg
      - POSTGRES_PASSWORD=spheroseg_dev
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - spheroseg-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U spheroseg -d spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend service
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: spheroseg-frontend
    ports:
      - "3000:5173"
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - ./index.html:/app/index.html
      - ./vite.config.ts:/app/vite.config.ts
      - ./tailwind.config.ts:/app/tailwind.config.ts
      - ./postcss.config.js:/app/postcss.config.js
      - ./tsconfig.json:/app/tsconfig.json
      - ./tsconfig.app.json:/app/tsconfig.app.json
    environment:
      - VITE_API_BASE_URL=http://localhost:3001/api
      - VITE_ML_SERVICE_URL=http://localhost:8000
    depends_on:
      - backend
    networks:
      - spheroseg-network
    restart: unless-stopped

  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: ../docker/backend.Dockerfile
    container_name: spheroseg-backend
    env_file: .env
    ports:
      - "3001:3001"
    volumes:
      - ./backend/src:/app/src
      - ./backend/prisma:/app/prisma
      - ./backend/uploads:/app/uploads
      - backend-db:/app/data
    environment:
      - NODE_ENV=development
      - PORT=3001
      - HOST=0.0.0.0
      - DATABASE_URL=**************************************************/spheroseg
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:5173
      - SEGMENTATION_SERVICE_URL=http://ml-service:8000
      - UPLOAD_DIR=/app/uploads
      - STORAGE_TYPE=local
      - EMAIL_SERVICE=sendgrid
      - SENDGRID_API_KEY=dummy-key-for-development
      - FROM_EMAIL=<EMAIL>
      - JWT_ACCESS_SECRET=${JWT_ACCESS_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX=50000
    depends_on:
      postgres:
        condition: service_healthy
      ml-service:
        condition: service_started
    networks:
      - spheroseg-network
    restart: unless-stopped

  # ML Service
  ml-service:
    build:
      context: ./backend/segmentation
      dockerfile: ../../docker/ml.Dockerfile
    container_name: spheroseg-ml
    ports:
      - "8000:8000"
    volumes:
      - ./backend/segmentation:/app
      - ./backend/segmentation/weights:/app/weights
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - DISPLAY=:99
      - QT_QPA_PLATFORM=offscreen
    networks:
      - spheroseg-network
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    restart: unless-stopped

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: spheroseg-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - spheroseg-network
    restart: unless-stopped

  # Grafana visualization
  grafana:
    image: grafana/grafana:latest
    container_name: spheroseg-grafana
    ports:
      - "3030:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=${GF_SECURITY_ADMIN_USER:-spheroseg_admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD:-change_me_in_production}
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - spheroseg-network
    depends_on:
      - prometheus
    restart: unless-stopped

networks:
  spheroseg-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  ml-models:
    driver: local
  backend-db:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local