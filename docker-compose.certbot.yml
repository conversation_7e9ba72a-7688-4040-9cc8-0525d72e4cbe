version: '3.8'

services:
  # Certbot for Let's Encrypt SSL automation
  certbot:
    image: certbot/certbot:latest
    container_name: spheroseg-certbot
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt
      - /var/lib/letsencrypt:/var/lib/letsencrypt
      - ./docker/nginx/certbot:/var/www/certbot
      - ./scripts/certbot-logs:/var/log/letsencrypt
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do sleep 12h & wait $${!}; certbot renew; done;'"
    restart: unless-stopped
    depends_on:
      - nginx

networks:
  default:
    external:
      name: spheroseg-network