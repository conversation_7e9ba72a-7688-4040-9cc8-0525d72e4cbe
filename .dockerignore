# Git
.git
.gitignore

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Dependency directories
node_modules/

# Docker
.dockerignore
Dockerfile*
docker-compose*

# Environment
.env*

# Build outputs
dist
build

# Temporary files
tmp
temp

# Documentation
*.md
docs/

# Test files
test/
tests/
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx