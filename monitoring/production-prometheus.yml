global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'backend'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['green-backend:3001']
        labels:
          environment: 'production'
          service: 'backend'

  - job_name: 'ml-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['green-ml:8000']
        labels:
          environment: 'production'
          service: 'ml-service'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
        labels:
          environment: 'production'
          service: 'postgres'

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
        labels:
          environment: 'production'
          service: 'redis'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          environment: 'production'
          service: 'system'
