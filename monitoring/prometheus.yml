global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'spheroseg-monitor'

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'backend-combined'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'backend-business'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/api/metrics/business'
    scrape_interval: 60s

  - job_name: 'ml-service'
    static_configs:
      - targets: ['ml-service:8000']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres_exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis_exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-prometheus-exporter:9113']
    metrics_path: '/metrics'
