# Backend service log rotation configuration for SpheroSeg
/var/log/spheroseg/backend/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 node node
    sharedscripts
    copytruncate
    
    # Backend logs include application logs, access logs, and error logs
    postrotate
        # Send SIGUSR1 to backend process to reopen log files if using file logging
        # This is optional as most Node.js apps use stdout/stderr
        if [ -f /var/run/spheroseg-backend.pid ]; then
            kill -USR1 `cat /var/run/spheroseg-backend.pid` 2>/dev/null || true
        fi
        
        # Optionally restart the backend service if needed
        # docker restart spheroseg-backend 2>/dev/null || true
    endscript
}