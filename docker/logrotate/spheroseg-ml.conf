# ML service log rotation configuration for SpheroSeg
/var/log/spheroseg/ml/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 mluser mluser
    sharedscripts
    copytruncate
    
    # ML service logs include inference logs, model loading logs, and error logs
    postrotate
        # Send SIGUSR1 to ML service process to reopen log files
        if [ -f /var/run/spheroseg-ml.pid ]; then
            kill -USR1 `cat /var/run/spheroseg-ml.pid` 2>/dev/null || true
        fi
        
        # Clean up temporary files older than 7 days
        find /tmp/spheroseg-ml-* -type f -mtime +7 -delete 2>/dev/null || true
        
        # Optionally restart the ML service if needed
        # docker restart spheroseg-ml 2>/dev/null || true
    endscript
}