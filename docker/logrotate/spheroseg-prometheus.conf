# Prometheus and monitoring service log rotation configuration for SpheroSeg
/var/log/spheroseg/prometheus/*.log /var/log/spheroseg/grafana/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 prometheus prometheus
    sharedscripts
    copytruncate
    
    # Monitoring service logs
    postrotate
        # Restart monitoring services if needed
        # docker restart spheroseg-prometheus 2>/dev/null || true
        # docker restart spheroseg-grafana 2>/dev/null || true
        
        # Clean up old metric files (older than 30 days)
        find /var/log/spheroseg/prometheus -name "*.log.*.gz" -mtime +30 -delete 2>/dev/null || true
        find /var/log/spheroseg/grafana -name "*.log.*.gz" -mtime +30 -delete 2>/dev/null || true
    endscript
}