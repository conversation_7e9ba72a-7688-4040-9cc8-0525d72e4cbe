# Nginx log rotation configuration for SpheroSeg
/var/log/nginx/access.log /var/log/nginx/error.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 nginx nginx
    sharedscripts
    prerotate
        if [ -d /etc/logrotate.d/httpd-prerotate ] && [ -n "$(find /etc/logrotate.d/httpd-prerotate -type f -executable 2>/dev/null)" ]; then
            run-parts /etc/logrotate.d/httpd-prerotate;
        fi
    endscript
    postrotate
        # Test nginx configuration before reloading
        if [ -f /var/run/nginx.pid ]; then
            nginx -t > /dev/null 2>&1 && kill -USR1 `cat /var/run/nginx.pid`
        fi
    endscript
}