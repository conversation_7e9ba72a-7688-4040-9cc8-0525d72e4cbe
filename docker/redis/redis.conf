# Redis configuration for production
# This file should be generated from redis.conf.template
# DO NOT COMMIT actual passwords to this file

# Basic settings
port 6379
# Bind to Docker network interface only (not public)
bind 127.0.0.1 ::1
protected-mode yes
timeout 0
tcp-keepalive 300

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Logging
loglevel notice

# Max memory policy
maxmemory-policy allkeys-lru