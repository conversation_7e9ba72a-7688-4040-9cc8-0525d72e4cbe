apiVersion: 1

policies:
  - orgId: 1
    receiver: grafana-default-email
    group_by: ['alertname']
    group_wait: 10s
    group_interval: 5m
    repeat_interval: 12h
    routes:
      - receiver: critical-alerts
        matchers:
          - severity = critical
        group_wait: 10s
        group_interval: 2m
        repeat_interval: 1h
      - receiver: warning-alerts
        matchers:
          - severity = warning
        group_wait: 30s
        group_interval: 5m
        repeat_interval: 4h

contactPoints:
  - orgId: 1
    name: grafana-default-email
    receivers:
      - uid: default-email
        type: email
        settings:
          addresses: ${ALERT_EMAIL}
          subject: '[SpheroSeg] Alert: {{ .GroupLabels.alertname }}'
          message: |
            {{ range .Alerts }}
            **Alert:** {{ .Annotations.summary }}
            **Description:** {{ .Annotations.description }}
            **Severity:** {{ .Labels.severity }}
            **Service:** {{ .Labels.service }}
            **Starts At:** {{ .StartsAt }}
            {{ end }}

  - orgId: 1
    name: critical-alerts
    receivers:
      - uid: critical-email
        type: email
        settings:
          addresses: ${ALERT_EMAIL}
          subject: '[CRITICAL] SpheroSeg Alert: {{ .GroupLabels.alertname }}'
          message: |
            🚨 **CRITICAL ALERT** 🚨
            
            {{ range .Alerts }}
            **Alert:** {{ .Annotations.summary }}
            **Description:** {{ .Annotations.description }}
            **Severity:** {{ .Labels.severity }}
            **Service:** {{ .Labels.service }}
            **Time:** {{ .StartsAt }}
            {{ end }}
            
            Please investigate immediately!

  - orgId: 1
    name: warning-alerts
    receivers:
      - uid: warning-email
        type: email
        settings:
          addresses: ${ALERT_EMAIL}
          subject: '[WARNING] SpheroSeg Alert: {{ .GroupLabels.alertname }}'
          message: |
            ⚠️ **WARNING ALERT** ⚠️
            
            {{ range .Alerts }}
            **Alert:** {{ .Annotations.summary }}
            **Description:** {{ .Annotations.description }}
            **Severity:** {{ .Labels.severity }}
            **Service:** {{ .Labels.service }}
            **Time:** {{ .StartsAt }}
            {{ end }}