user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript 
               application/json application/javascript application/xml+rss 
               application/rss+xml application/atom+xml image/svg+xml 
               text/x-js text/x-cross-domain-policy application/x-font-ttf 
               application/x-font-opentype application/vnd.ms-fontobject 
               image/x-icon;

    # Cache settings
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=app_cache:10m 
                     max_size=1g inactive=60m use_temp_path=off;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=60r/m;
    limit_req_zone $binary_remote_addr zone=upload_limit:10m rate=10r/s;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Hide nginx version
    server_tokens off;

    # DNS resolver for Docker internal network
    resolver 127.0.0.11 valid=30s;
    
    # UNIFIED UPSTREAM DEFINITIONS
    # These will be dynamically updated by deployment scripts
    upstream backend {
        server green-backend:3001;
    }

    upstream ml_service {
        server green-ml:8000;
    }

    upstream frontend {
        server green-frontend:80;
    }


    # Main server configuration
    server {
        listen 80;
        listen 443 ssl http2;
        server_name spherosegapp.utia.cas.cz;

        # SSL configuration
        ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Redirect HTTP to HTTPS
        if ($scheme != "https") {
            return 301 https://$host$request_uri;
        }

        # Security headers per location
        add_header X-Environment "blue-green-production" always;

        # Socket.IO WebSocket endpoint (MUST be first for proper routing)
        location /socket.io/ {
            proxy_pass http://backend/socket.io/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket timeouts - critical for real-time notifications
            proxy_connect_timeout 7d;
            proxy_send_timeout 7d;
            proxy_read_timeout 7d;
            
            # Disable buffering for WebSocket
            proxy_buffering off;
            
            # Debug header
            add_header X-Nginx-Location "websocket" always;
        }

        # Static uploads (images, thumbnails) - HIGH PRIORITY
        location ^~ /uploads/ {
            proxy_pass http://backend/uploads/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache static content
            proxy_cache app_cache;
            proxy_cache_valid 200 304 12h;
            proxy_cache_valid 404 1m;
            add_header X-Cache-Status $upstream_cache_status;
            
            # Security headers for images
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-Nginx-Location "uploads" always;
            
            # Cache control
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        # ML service routes (/api/ml/*) - MUST be before general /api/
        location /api/ml/ {
            limit_req zone=api_limit burst=10 nodelay;
            # CRITICAL: Trailing slash to strip /api/ml/ prefix (ML service expects root paths)
            proxy_pass http://ml_service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for ML operations
            proxy_connect_timeout 60s;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
            
            # Body size for image uploads
            client_max_body_size 100M;
            
            # Debug header
            add_header X-Nginx-Location "ml-service" always;
        }

        # API routes (general backend) - PRESERVE /api/ prefix
        location /api/ {
            limit_req zone=api_limit burst=20 nodelay;
            # CRITICAL: No trailing slash to preserve /api/ prefix
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Important for file uploads
            proxy_http_version 1.1;
            proxy_request_buffering off;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Body size for file uploads
            client_max_body_size 100M;
            
            # Debug header
            add_header X-Nginx-Location "api-backend" always;
        }

        # API Documentation - specific routing
        location /api-docs {
            proxy_pass http://backend/api-docs;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }


        # Frontend - catch-all (MUST be last)
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support for Vite HMR (development)
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Cache control for HTML files
            location ~* \.(html|htm)$ {
                proxy_pass http://frontend;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }
            
            # Cache static assets with hashes
            location ~* \.(js|css|map|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                proxy_pass http://frontend;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
            
            # Debug header
            add_header X-Nginx-Location "frontend" always;
        }

        # Let's Encrypt ACME challenge
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
            allow all;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "blue-green-production-healthy\n";
            add_header Content-Type text/plain;
            add_header X-Environment "blue-green-production";
        }

        # Nginx status (for monitoring)
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 172.16.0.0/12;  # Docker networks
            deny all;
        }
    }
}