# Nginx configuration for staging internal service
# This runs inside the staging nginx container

user nginx;
worker_processes 1;  # Less workers for staging
error_log /var/log/nginx/error.log warn;

events {
    worker_connections 1024;  # Reduced for staging
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations (reduced for staging)
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;  # Shorter for staging
    types_hash_max_size 1024;
    client_max_body_size 50M;  # Smaller for staging
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 4;  # Less compression for staging
    gzip_types text/plain text/css text/xml text/javascript 
               application/json application/javascript application/xml+rss 
               application/rss+xml application/atom+xml image/svg+xml;

    # Hide nginx version
    server_tokens off;

    # Simple server block for staging frontend
    server {
        listen 80;
        server_name localhost;

        # Add staging headers
        add_header X-Environment "staging" always;
        add_header X-Robots-Tag "noindex, nofollow" always;

        # Root location - Frontend static files
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
            
            # Cache control for staging (shorter)
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "staging-nginx-healthy\n";
            add_header Content-Type text/plain;
            add_header X-Environment "staging" always;
        }

        # Static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            root /usr/share/nginx/html;
            expires 1d;
            add_header Cache-Control "public, max-age=86400";
            add_header X-Environment "staging" always;
        }

        # Deny access to hidden files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
}