user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
# pid /tmp/nginx.pid; # Commented out to avoid permission issues

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript 
               application/json application/javascript application/xml+rss 
               application/rss+xml application/atom+xml image/svg+xml 
               text/x-js text/x-cross-domain-policy application/x-font-ttf 
               application/x-font-opentype application/vnd.ms-fontobject 
               image/x-icon;

    # Cache settings
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=app_cache:10m 
                     max_size=1g inactive=60m use_temp_path=off;

    # Rate limiting - increased for data-heavy applications  
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=60r/m;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Hide nginx version
    server_tokens off;

    # Production Upstream definitions
    upstream backend {
        least_conn;
        server spheroseg-backend:3001 max_fails=3 fail_timeout=30s;
    }

    upstream ml_service {
        least_conn;
        server spheroseg-ml:8000 max_fails=3 fail_timeout=30s;
    }

    upstream grafana {
        server spheroseg-grafana:3000;
    }

    # Staging Upstream definitions
    upstream staging_backend {
        least_conn;
        server staging-backend:3001 max_fails=3 fail_timeout=30s;
    }

    upstream staging_ml_service {
        least_conn;
        server staging-ml:8000 max_fails=3 fail_timeout=30s;
    }

    upstream staging_grafana {
        server staging-grafana:3000;
    }

    upstream staging_nginx {
        server staging-nginx:80;
    }

    # Include site configurations
    include /etc/nginx/sites-enabled/*.conf;
}