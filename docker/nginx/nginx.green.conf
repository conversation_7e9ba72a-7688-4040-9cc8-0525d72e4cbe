server {
    listen 80;
    listen [::]:80;
    server_name spherosegapp.utia.cas.cz;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name spherosegapp.utia.cas.cz;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Uploads - serve static files from backend (must be before catch-all)
    location ^~ /uploads/ {
        proxy_pass http://green-backend:3001/uploads/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Debug header to verify this location is being used
        add_header X-Nginx-Location "uploads" always;
        
        # Cache images
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # Frontend - proxy to GREEN environment
    location / {
        proxy_pass http://green-frontend:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Prevent caching of HTML to avoid stale module references
        location ~* \.(html|htm)$ {
            proxy_pass http://green-frontend:80;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }
    
    # Cache static assets with hash in filename (Vite generates these)
    location ~* \.(js|css|map)$ {
        proxy_pass http://green-frontend:80;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        
        # If file has hash in name (e.g., Dashboard-CJafRIjT.js), cache it
        # Otherwise no cache for dynamic imports
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # WebSocket support for Socket.io
    location /socket.io/ {
        proxy_pass http://green-backend:3001/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket timeouts
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }
    
    # API Backend - proxy to GREEN backend  
    location /api/ {
        proxy_pass http://green-backend:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        
        # Body size for file uploads
        client_max_body_size 100M;
    }
    
    # ML Service endpoints
    location /api/ml/ {
        proxy_pass http://green-ml:8000/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Increased timeouts for ML processing
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        
        # Body size for image uploads
        client_max_body_size 100M;
    }
    
    # API Documentation
    location /api-docs {
        proxy_pass http://green-backend:3001/api-docs;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
}