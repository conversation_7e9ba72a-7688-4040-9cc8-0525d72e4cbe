# Staging Subdomain Configuration
# HTTP server for staging (temporary for testing)
server {
    listen 80;
    listen [::]:80;
    server_name staging.spherosegapp.utia.cas.cz;
    
    # Staging banner headers
    add_header X-Environment "staging" always;
    add_header X-Robots-Tag "noindex, nofollow" always;
    
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "staging-environment-healthy\n";
        add_header Content-Type text/plain;
        add_header X-Environment "staging" always;
    }
    
    # Frontend static files (proxy to staging nginx)
    location / {
        proxy_pass http://staging_nginx;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "staging";
        
        # Add staging headers
        add_header X-Environment "staging" always;
        add_header X-Robots-Tag "noindex, nofollow" always;
    }
    
    # API routes (proxy to staging backend)
    location /api/ {
        proxy_pass http://staging_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "staging";
        
        # Rate limiting
        limit_req zone=api_limit burst=20 nodelay;
        
        # Add staging headers
        add_header X-Environment "staging" always;
    }
    
    # ML API routes (proxy to staging ML service) 
    location /api/ml/ {
        proxy_pass http://staging_ml_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "staging";
        
        # Longer timeout for ML operations
        proxy_read_timeout 300s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        
        # Add staging headers
        add_header X-Environment "staging" always;
    }
    
    # Grafana (proxy to staging grafana)
    location /grafana/ {
        proxy_pass http://staging_grafana/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Environment "staging";
        
        # Add staging headers
        add_header X-Environment "staging" always;
    }
}

# HTTPS server for staging
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    http2 on;
    server_name staging.spherosegapp.utia.cas.cz;

    # SSL configuration - will use wildcard or staging-specific certificate
    ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/chain.pem;
    
    # SSL security settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;

    # HSTS - shorter for staging
    add_header Strict-Transport-Security "max-age=86400; includeSubDomains" always;

    # CSP Header - Staging configuration (more permissive)
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; font-src 'self' data:; connect-src 'self' https://staging.spherosegapp.utia.cas.cz wss://staging.spherosegapp.utia.cas.cz; frame-ancestors 'none'; upgrade-insecure-requests;" always;

    # Staging banner header
    add_header X-Environment "staging" always;
    add_header X-Robots-Tag "noindex, nofollow" always;

    # Cache static assets (shorter for staging)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://staging_nginx;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        add_header X-Environment "staging" always;
    }

    # Root location - Frontend static files
    location / {
        proxy_pass http://staging_nginx;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header X-Environment "staging" always;
        add_header X-Robots-Tag "noindex, nofollow" always;
    }

    # API endpoints
    location /api {
        limit_req zone=api_limit burst=200 nodelay;  # More permissive for staging
        
        proxy_pass http://staging_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Staging environment header
        add_header X-Environment "staging" always;
        
        # Increase timeout for long operations
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
    }

    # ML Service endpoints
    location /api/ml {
        rewrite ^/api/ml(/.*)$ $1 break;
        
        proxy_pass http://staging_ml_service;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Staging environment header
        add_header X-Environment "staging" always;
        
        # ML operations need longer timeouts
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_read_timeout 600;
        
        # Disable buffering for streaming responses
        proxy_buffering off;
    }

    # Serve uploaded files
    location /uploads/ {
        proxy_pass http://staging_backend/uploads/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache uploaded images (shorter for staging)
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        add_header X-Environment "staging" always;
    }

    # Socket.io support
    location /socket.io/ {
        proxy_pass http://staging_backend/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-NginX-Proxy true;
        
        # WebSocket timeout
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 60s;
        
        # Disable buffering for socket.io
        proxy_buffering off;
        proxy_cache off;
    }
    
    # Legacy WebSocket support (if needed)
    location /ws {
        proxy_pass http://staging_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket timeout
        proxy_read_timeout 3600;
        proxy_send_timeout 3600;
    }

    # Grafana monitoring for staging
    location /grafana/ {
        proxy_pass http://staging_grafana/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header X-Environment "staging" always;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "staging-healthy\n";
        add_header Content-Type text/plain;
        add_header X-Environment "staging" always;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}