# Prometheus alerting rules for SpheroSeg Cell Segmentation Hub
groups:
  - name: spheroseg_infrastructure
    rules:
      # High CPU usage alert
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 2m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 2 minutes on {{ $labels.instance }}"

      # High memory usage alert
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
        for: 2m
        labels:
          severity: critical
          service: infrastructure
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 90% for more than 2 minutes on {{ $labels.instance }}"

      # Low disk space alert
      - alert: LowDiskSpace
        expr: ((node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_avail_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"}) * 100 > 90
        for: 1m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Low disk space detected"
          description: "Disk usage is above 90% on {{ $labels.instance }} at {{ $labels.mountpoint }}"
          
  - name: spheroseg_application
    rules:
      # High API response time alert
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, sum by (le, instance)(rate(http_request_duration_seconds_bucket{job="spheroseg-backend"}[5m]))) > 2
        for: 1m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High API response time detected"
          description: "95th percentile API response time is above 2 seconds for {{ $labels.instance }}"

      # High error rate alert
      - alert: HighErrorRate
        expr: (sum by (instance)(rate(http_requests_total{job="spheroseg-backend",status=~"5.."}[5m])) / sum by (instance)(rate(http_requests_total{job="spheroseg-backend"}[5m]))) * 100 > 5
        for: 1m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for {{ $labels.instance }}"

      # Service down alert
      - alert: ServiceDown
        expr: up{job=~"spheroseg-backend|spheroseg-ml"} == 0
        for: 30s
        labels:
          severity: critical
          service: application
        annotations:
          summary: "Service is not responding"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} is down"
          job: "{{ $labels.job }}"

      # Database connection failures alert
      - alert: DatabaseConnectionFailures
        expr: increase(database_connection_errors_total{job="spheroseg-backend"}[5m]) > 5
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Database connectivity issues"
          description: "Database connection errors detected on {{ $labels.instance }}"