global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['prometheus:9090']

  # Backend API monitoring
  - job_name: 'spheroseg-backend'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # ML Service monitoring
  - job_name: 'spheroseg-ml'
    static_configs:
      - targets: ['ml-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Frontend doesn't have metrics endpoint - removed

# Alerting rules
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093