# Dependencies
node_modules/
**/node_modules/

# Build outputs
dist/
build/
coverage/
**/dist/**
**/build/**

# Docker
docker/
Dockerfile*
docker-compose*.yml

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Backend Python files
backend/
**/*.py

# Package files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
**/*.d.ts
**/*.generated.*

# Binary files
**/*.png
**/*.jpg
**/*.jpeg
**/*.gif
**/*.svg
**/*.ico
**/*.pdf
**/*.woff
**/*.woff2
**/*.ttf
**/*.eot

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db