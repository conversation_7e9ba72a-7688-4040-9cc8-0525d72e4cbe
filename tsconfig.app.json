{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting - Gradually enable stricter settings */
    "strict": false, // Keep false for now, but enable specific checks below
    "noUnusedLocals": false, // Keep false to avoid breaking existing code
    "noUnusedParameters": false, // Keep false to avoid breaking existing code
    "noImplicitAny": true, // Enable - helps catch undefined types
    "noFallthroughCasesInSwitch": true, // Enable - prevents switch fallthrough bugs
    "strictNullChecks": true, // Enable - helps catch null/undefined bugs
    "noImplicitReturns": true, // Enable - ensures all code paths return
    "noImplicitThis": true, // Enable - catches 'this' context issues

    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"]
}
