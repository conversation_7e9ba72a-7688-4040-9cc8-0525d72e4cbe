# Green Environment Configuration Example
# Copy this file to .env.green and fill in actual values

# Database Configuration
DB_HOST=postgres-green
DB_PORT=5432
DB_NAME=spheroseg_green
DB_USER=spheroseg
DB_PASSWORD=your-database-password

# JWT Secrets - Generate strong random secrets
GREEN_JWT_ACCESS_SECRET=your-jwt-access-secret
GREEN_JWT_REFRESH_SECRET=your-jwt-refresh-secret

# SMTP Configuration - Get from your email provider or IT department
# SMTP credentials - replace with actual values from secure storage
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password
SMTP_HOST=mail.example.com
SMTP_PORT=465
SMTP_SECURE=true

# Email Settings
FROM_EMAIL=<EMAIL>
SKIP_EMAIL_SEND=false
EMAIL_ALLOW_INSECURE=false
EMAIL_TIMEOUT=30000

# WebSocket Configuration
WS_ALLOWED_ORIGINS=https://your-domain.com

# Node Environment
NODE_ENV=production