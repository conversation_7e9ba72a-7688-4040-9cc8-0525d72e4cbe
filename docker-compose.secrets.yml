# Docker Compose secrets configuration for production
# Use: docker-compose -f docker-compose.prod.yml -f docker-compose.secrets.yml up -d

version: '3.8'

secrets:
  jwt_access_secret:
    external: true
    name: spheroseg_jwt_access_secret
  jwt_refresh_secret:
    external: true
    name: spheroseg_jwt_refresh_secret
  db_password:
    external: true
    name: spheroseg_db_password
  grafana_admin_password:
    external: true
    name: spheroseg_grafana_admin_password

services:
  # Backend API - Override to use secrets
  backend:
    secrets:
      - jwt_access_secret
      - jwt_refresh_secret
      - db_password
    environment:
      # Remove sensitive vars from environment and read from secrets
      - NODE_ENV=production
      - PORT=3001
      - HOST=0.0.0.0
      - DATABASE_URL_TEMPLATE=postgresql://spheroseg:{{DB_PASSWORD}}@spheroseg-db:5432/spheroseg_prod
      - REDIS_URL=redis://spheroseg-redis:6379
      - ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz
      - WS_ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz
      - SEGMENTATION_SERVICE_URL=http://spheroseg-ml:8000
      - UPLOAD_DIR=/app/uploads
      - STORAGE_TYPE=local
      - JWT_ACCESS_SECRET_FILE=/run/secrets/jwt_access_secret
      - JWT_REFRESH_SECRET_FILE=/run/secrets/jwt_refresh_secret
      - DB_PASSWORD_FILE=/run/secrets/db_password
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX=1000
      - LOG_LEVEL=info
      - ENABLE_METRICS=true

  # PostgreSQL Database - Override to use secrets
  postgres:
    secrets:
      - db_password
    environment:
      - POSTGRES_USER=spheroseg
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
      - POSTGRES_DB=spheroseg_prod
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - POSTGRES_HOST_AUTH_METHOD=md5
      - PGPORT=5432

  # Grafana - Override to use secrets
  grafana:
    secrets:
      - grafana_admin_password
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD_FILE=/run/secrets/grafana_admin_password
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=https://spherosegapp.utia.cas.cz/grafana
      - GF_SERVER_SERVE_FROM_SUB_PATH=true