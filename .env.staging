# Staging Environment Variables
# NEVER commit this file to version control!

# Node Environment (must be production/development/test for Zod validation)
NODE_ENV=production

# Database - Staging specific
STAGING_DB_PASSWORD=5486c3c52c893e783cdb9f09cc90c9a0edfdbad1755b82fdfcd49e2e7b26363f

# JWT Secrets - Staging specific (32 bytes / 256 bits)
STAGING_JWT_ACCESS_SECRET=1c4fb341de6e2812f81b0cd5287652d38518287b64289fb21346305bba6fe87d
STAGING_JWT_REFRESH_SECRET=fb5a22af8446e7ac670ea277715ec4bdf9e233efd55f5ddac749edd8f2e9562b

# JWT Configuration
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# API Configuration - Staging localhost
API_BASE_URL=http://localhost:4001

# Email Service (development/staging configuration)
EMAIL_SERVICE=smtp
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=dummy
SMTP_PASS=dummy
SENDGRID_API_KEY=
FROM_EMAIL=<EMAIL>

# Monitoring - Staging
STAGING_GRAFANA_ADMIN_USER=admin
STAGING_GRAFANA_ADMIN_PASSWORD=29e67bb0fb9352075e868c2d3e1cc440

# Session Secret - Staging
SESSION_SECRET=98b02479c454a863d6ed382e6706b7108d99c4ae9e435b0b2c0cf30119ba051c

# CORS Configuration - Staging subdomain
ALLOWED_ORIGINS=https://staging.spherosegapp.utia.cas.cz

# Upload Limits (same as production)
MAX_FILE_SIZE=104857600
MAX_FILES_PER_UPLOAD=10

# Rate Limiting - More permissive for staging
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=200

# Logging - More verbose for staging
LOG_LEVEL=debug
LOG_FORMAT=json

# Performance - Reduced for staging
CLUSTER_WORKERS=2
MAX_CONNECTIONS=50

# Staging specific features
STAGING_MODE=true
DEBUG_MODE=true