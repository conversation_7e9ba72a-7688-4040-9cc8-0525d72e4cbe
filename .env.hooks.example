# =============================================================================
# GIT HOOKS CONFIGURATION
# =============================================================================
# Copy this file to .env.hooks and adjust settings as needed
# Source it before commits: source .env.hooks
# =============================================================================

# PRE-COMMIT HOOK CONFIGURATION
# -----------------------------

# Strict mode - if true, any error blocks the commit
# Default: false (allows commit with warnings)
export STRICT_MODE=false

# Auto-fix - automatically fix formatting and linting issues
# Default: true
export AUTO_FIX=true

# Docker checks - run Docker-based validations
# Default: true
export DOCKER_CHECKS=true

# Skip tests - bypass test execution (NOT RECOMMENDED)
# Default: false
export SKIP_TESTS=false

# PRE-MERGE HOOK CONFIGURATION
# ----------------------------

# Docker timeout - max time for Docker operations (milliseconds)
# Default: 300000 (5 minutes)
export DOCKER_TIMEOUT=300000

# Target branch - default branch for merge checks
# Default: main
export TARGET_BRANCH=main

# GITHUB CONFIGURATION
# --------------------

# GitHub token for API operations (optional)
# Create at: https://github.com/settings/tokens
# Required scopes: repo (all)
# export GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxx

# GitHub repository (auto-detected from git remote)
# export GITHUB_OWNER=username
# export GITHUB_REPO=repository

# CI/CD CONFIGURATION
# -------------------

# Skip CI - add [skip ci] to commit messages
# export SKIP_CI=false

# Parallel jobs - number of parallel test jobs
# export PARALLEL_JOBS=4

# Coverage threshold - minimum code coverage percentage
# export COVERAGE_THRESHOLD=80

# NOTIFICATION SETTINGS
# --------------------

# Slack webhook for notifications (optional)
# export SLACK_WEBHOOK_URL=https://hooks.slack.com/services/xxx

# Email notifications (optional)
# export NOTIFICATION_EMAIL=<EMAIL>

# CUSTOM CHECKS
# -------------

# Additional scripts to run during pre-commit
# export CUSTOM_PRE_COMMIT_SCRIPTS="./scripts/custom-check.sh"

# Additional scripts to run during pre-merge
# export CUSTOM_PRE_MERGE_SCRIPTS="./scripts/custom-validation.sh"

# DEBUG OPTIONS
# -------------

# Verbose output - show detailed information
# export VERBOSE=false

# Debug mode - show all commands being executed
# export DEBUG=false

# Dry run - simulate actions without making changes
# export DRY_RUN=false