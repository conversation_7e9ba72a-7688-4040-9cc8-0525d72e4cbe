# Development Environment Configuration (Parallel Deployment)

# API Configuration
VITE_API_URL=http://localhost:3001
VITE_API_BASE_URL=http://localhost:3001/api
VITE_ML_SERVICE_URL=http://localhost:3001/api/ml
VITE_WS_URL=ws://localhost:3001

# Development Configuration
NODE_ENV=development

# JWT Configuration (development secrets - DO NOT USE IN PRODUCTION)
JWT_ACCESS_SECRET=8baa62d8e76ac9902695cf92eddaaca4163195c8531b2d6944d6ae22ad27abc1
JWT_REFRESH_SECRET=079411cbd11be72f210392b481ef18b30cc7e86d77ad86a7dc907ec247c7c58a

# Database Configuration
DATABASE_URL=******************************************************/spheroseg_dev

# Redis Configuration
REDIS_URL=redis://dev-redis:6379

# Service URLs
SEGMENTATION_SERVICE_URL=http://dev-ml:8000
FRONTEND_URL=http://localhost:3000

# ML Service Configuration
ML_INFERENCE_TIMEOUT=60

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000
WS_ALLOWED_ORIGINS=http://localhost:3000

# Email Configuration (for development - using MailHog)
EMAIL_SERVICE=smtp
FROM_EMAIL=<EMAIL>
FROM_NAME=SpheroSeg Platform
SMTP_HOST=dev-mailhog
SMTP_PORT=1025

# Optional: Control Docker container behaviors
PRISMA_DB_PUSH=false
FASTAPI_RELOAD=true

# Grafana Configuration
GF_SECURITY_ADMIN_PASSWORD=admin
GF_SECURITY_ADMIN_USER=admin

# Port Mapping (Development)
# Frontend: 3000
# Backend: 3001
# ML Service: 8000
# PostgreSQL: 5433
# Redis: 6380
# MailHog Web: 8025
# MailHog SMTP: 1025
# Prometheus: 9090
# Grafana: 3030