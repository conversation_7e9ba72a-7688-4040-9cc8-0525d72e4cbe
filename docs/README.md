# Cell Segmentation Hub - Documentation

Welcome to the comprehensive documentation for the Cell Segmentation Hub, a modern web application for AI-powered cell segmentation analysis.

## Documentation Structure

### 📚 [API Documentation](./api/)

Complete reference for all REST API endpoints

- [Authentication](./api/authentication.md) - User auth and JWT tokens
- [Projects](./api/projects.md) - Project management endpoints
- [Images](./api/images.md) - Image upload and management
- [Segmentation](./api/segmentation.md) - ML segmentation services

### 🏗️ [Architecture](./architecture/)

System design and component documentation

- [Overview](./architecture/README.md) - High-level architecture
- [Frontend](./architecture/frontend.md) - React app structure
- [Backend](./architecture/backend.md) - Node.js API design
- [ML Service](./architecture/ml-service.md) - Python segmentation service

### 🚀 [Development](./development/)

Developer setup and contribution guide

- [Getting Started](./development/getting-started.md) - Local development setup
- [Contributing](./development/contributing.md) - Contribution guidelines
- [Testing](./development/testing.md) - Testing procedures

### 📦 [Deployment](./deployment/)

Production deployment guides

- [Docker Setup](./deployment/docker.md) - Container deployment
- [Production Guide](./deployment/production.md) - Production configuration

### 📖 [Guides](./guides/)

User and administrator guides

- [User Guide](./guides/user-guide.md) - Application usage
- [Admin Guide](./guides/admin-guide.md) - Administration tasks

### 📋 [Reference](./reference/)

Technical reference materials

- [Database Schema](./reference/database-schema.md) - Complete database structure
- [ML Models](./reference/ml-models.md) - Available segmentation models
- [Claude Instructions](./reference/claude-instructions.md) - AI assistant guidelines
- [Refactoring History](./reference/refactoring-summary.md) - Development history

## Quick Links

- **🏠 [Main README](../README.md)** - Project overview and quick start
- **🔧 [Development Setup](./development/getting-started.md)** - Start developing locally
- **🐳 [Docker Deployment](./deployment/docker.md)** - Deploy with containers
- **📡 [API Reference](./api/README.md)** - Complete API documentation

## Need Help?

- Check the appropriate documentation section above
- Look at the [troubleshooting guides](./development/testing.md)
- Review the [architecture documentation](./architecture/) for system understanding
